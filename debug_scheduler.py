#!/usr/bin/env python3
"""
Debug script to check scheduler timing and timezone issues
"""
import sys
import json
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def debug_timezone_and_schedule():
    """Debug timezone handling and schedule execution"""
    print("🕐 Debugging Scheduler Timezone and Execution")
    print("=" * 60)
    
    # Setup timezone
    tehran_tz = pytz.timezone('Asia/Tehran')
    
    # Current times
    now_utc = datetime.utcnow()
    now_tehran = pytz.UTC.localize(now_utc).astimezone(tehran_tz)
    
    print(f"Current UTC time: {now_utc}")
    print(f"Current Tehran time: {now_tehran}")
    print(f"Current Tehran hour: {now_tehran.hour}")
    print(f"Posting window: 9-24 hours")
    print(f"In posting window: {9 <= now_tehran.hour < 24}")
    print()
    
    # Load and analyze schedule
    schedule_file = Path("post_schedule.json")
    if not schedule_file.exists():
        print("❌ No post_schedule.json file found!")
        return
    
    try:
        with open(schedule_file, 'r', encoding='utf-8') as f:
            schedule_data = json.load(f)
    except Exception as e:
        print(f"❌ Error reading schedule file: {e}")
        return
    
    scheduled_posts = schedule_data.get('scheduled_posts', [])
    print(f"📋 Found {len(scheduled_posts)} scheduled posts")
    print()
    
    # Analyze each post
    posts_ready = 0
    posts_future = 0
    posts_other_status = 0
    
    for i, post in enumerate(scheduled_posts):
        status = post.get('status', 'unknown')
        scheduled_time_str = post.get('scheduled_time', '')
        post_id = post.get('id', f'post_{i}')
        
        print(f"Post {i+1}: {post_id}")
        print(f"  Status: {status}")
        print(f"  Scheduled time (stored): {scheduled_time_str}")
        
        if status != 'scheduled':
            posts_other_status += 1
            print(f"  ⏭️  Skipping (status: {status})")
            print()
            continue
        
        try:
            # Parse as stored (UTC without timezone)
            scheduled_naive = datetime.fromisoformat(scheduled_time_str)
            scheduled_utc = pytz.UTC.localize(scheduled_naive)
            scheduled_tehran = scheduled_utc.astimezone(tehran_tz)
            
            print(f"  Scheduled UTC: {scheduled_utc}")
            print(f"  Scheduled Tehran: {scheduled_tehran}")
            
            # Check if should execute
            should_execute = pytz.UTC.localize(now_utc) >= scheduled_utc
            time_diff = scheduled_utc - pytz.UTC.localize(now_utc)
            
            if should_execute:
                posts_ready += 1
                print(f"  ✅ READY TO EXECUTE (overdue by {abs(time_diff)})")
            else:
                posts_future += 1
                print(f"  ⏰ Future post (in {time_diff})")
                
            # Check posting window
            in_window = 9 <= scheduled_tehran.hour < 24
            print(f"  📅 In posting window: {in_window}")
            
        except Exception as e:
            print(f"  ❌ Error parsing time: {e}")
        
        print()
    
    print("=" * 60)
    print(f"📊 Summary:")
    print(f"  Posts ready to execute: {posts_ready}")
    print(f"  Future posts: {posts_future}")
    print(f"  Posts with other status: {posts_other_status}")
    print(f"  Currently in posting window: {9 <= now_tehran.hour < 24}")
    print()
    
    if posts_ready > 0 and 9 <= now_tehran.hour < 24:
        print("🚨 ISSUE: Posts are ready but not executing!")
        print("   Possible causes:")
        print("   1. App not running")
        print("   2. Instagram posting disabled")
        print("   3. Instagram authentication issues")
        print("   4. Rate limiting")
    elif posts_ready > 0:
        print("⏰ Posts ready but outside posting window")
    elif posts_future > 0:
        print("✅ All posts are scheduled for future times")
    else:
        print("📭 No scheduled posts found")

def test_scheduler_execution():
    """Test the actual scheduler execution"""
    print("\n🔧 Testing Scheduler Execution")
    print("=" * 60)
    
    try:
        from src.scheduler import PostScheduler
        from src.config import Config
        
        print(f"Instagram posting enabled: {Config.ENABLE_INSTAGRAM_POSTING}")
        print(f"Telegram video sending enabled: {Config.ENABLE_TELEGRAM_VIDEO_SENDING}")
        print()
        
        scheduler = PostScheduler()
        print(f"Scheduler created with {len(scheduler.scheduled_posts)} posts")
        
        # Test the check method
        print("\nRunning check_and_execute_posts()...")
        scheduler.check_and_execute_posts()
        print("Check completed")
        
    except Exception as e:
        print(f"❌ Error testing scheduler: {e}")
        import traceback
        traceback.print_exc()

def check_instagram_status():
    """Check Instagram client status"""
    print("\n📱 Checking Instagram Status")
    print("=" * 60)
    
    try:
        from src.instagram_client import InstagramClient
        
        client = InstagramClient()
        print(f"Session file exists: {client.session_file.exists()}")
        
        # Test login
        print("Testing Instagram login...")
        success = client.login()
        print(f"Login successful: {success}")
        
        if success:
            print(f"Can post now: {client._can_post_now()}")
            if client.last_post_time:
                import time
                time_since_last = time.time() - client.last_post_time
                print(f"Time since last post: {time_since_last/60:.1f} minutes")
                print(f"Min interval: {client.min_interval_seconds/60:.1f} minutes")
            else:
                print("No previous post time recorded")
        
    except Exception as e:
        print(f"❌ Error checking Instagram: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        debug_timezone_and_schedule()
        test_scheduler_execution()
        check_instagram_status()
        
        print("\n🎯 Debug completed!")
        
    except Exception as e:
        print(f"\n💥 Debug failed: {e}")
        import traceback
        traceback.print_exc()
