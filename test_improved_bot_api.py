#!/usr/bin/env python3
"""
Test the improved Bot API implementation
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_improved_bot_api():
    """Test the improved Bot API message retrieval"""
    print("🔧 Testing Improved Bot API")
    print("=" * 50)
    
    try:
        from src.telegram_client import TelegramClient
        
        # Create Bot API client
        bot_client = TelegramClient()
        
        print("🔄 Initializing Bot API client...")
        if await bot_client.initialize():
            print("✅ Bot API client initialized")
            
            # Test message retrieval with improved method
            print("\n📡 Testing improved message retrieval...")
            messages = await bot_client.get_recent_messages(limit=10, for_polling=True)
            
            if messages:
                print(f"✅ Retrieved {len(messages)} messages")
                
                # Show message details
                tehran_tz = pytz.timezone('Asia/Tehran')
                now_tehran = datetime.now(tehran_tz)
                
                print(f"\n📊 Message Analysis:")
                for i, msg in enumerate(messages):
                    msg_tehran = msg.date.astimezone(tehran_tz)
                    age = now_tehran - msg_tehran
                    age_hours = age.total_seconds() / 3600
                    
                    reaction_count = getattr(msg, 'reaction_count', 0)
                    
                    print(f"  {i+1}. Message #{msg.message_id}")
                    print(f"     Tehran time: {msg_tehran.strftime('%H:%M %d/%m')}")
                    print(f"     Age: {age_hours:.1f} hours")
                    print(f"     Reactions: {reaction_count}")
                    print(f"     Text: {(msg.text or msg.caption or 'No text')[:50]}...")
                    
                    # Check if this is recent
                    if msg.message_id >= 22940:  # Very recent
                        print(f"     ✅ EXCELLENT - Very recent message!")
                    elif msg.message_id >= 22930:  # Recent
                        print(f"     ✅ GOOD - Recent message")
                    else:
                        print(f"     ❌ OLD - Not recent enough")
                    print()
                
                # Check overall quality
                very_recent = sum(1 for msg in messages if msg.message_id >= 22940)
                recent = sum(1 for msg in messages if msg.message_id >= 22930)
                
                print(f"📈 Very recent messages (ID >= 22940): {very_recent}/{len(messages)}")
                print(f"📈 Recent messages (ID >= 22930): {recent}/{len(messages)}")
                
                if very_recent >= 1:
                    print("🎉 EXCELLENT: Getting very recent messages!")
                    return True
                elif recent >= len(messages) // 2:
                    print("✅ GOOD: Getting mostly recent messages!")
                    return True
                else:
                    print("⚠️  Still getting old messages")
                    return False
            else:
                print("❌ No messages retrieved")
                return False
        else:
            print("❌ Failed to initialize Bot API client")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        try:
            await bot_client.close()
        except:
            pass

async def test_message_selection():
    """Test message selection with improved Bot API"""
    print("\n🎯 Testing Message Selection")
    print("=" * 50)
    
    try:
        # Temporarily disable User API to test Bot API
        import os
        original_value = os.environ.get('USE_TELEGRAM_USER_API', 'false')
        os.environ['USE_TELEGRAM_USER_API'] = 'false'
        
        # Force reload config
        if 'src.config' in sys.modules:
            del sys.modules['src.config']
        
        from src.message_processor import MessageProcessor
        
        # Create message processor
        processor = MessageProcessor()
        
        print("🔄 Testing message selection with improved Bot API...")
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected = processed_messages[0]
            msg_id = selected.telegram_message.message_id
            reactions = getattr(selected.telegram_message, 'reaction_count', 0)
            
            print(f"✅ Selected message: #{msg_id}")
            print(f"🔥 Reactions: {reactions}")
            print(f"📝 Content: {selected.text_content[:100]}...")
            
            # Check message quality
            if msg_id >= 22940:
                print("🎉 PERFECT: Selected a very recent message!")
                return True
            elif msg_id >= 22930:
                print("✅ GOOD: Selected a recent message!")
                return True
            else:
                print(f"⚠️  Selected older message #{msg_id}")
                return False
        else:
            print("❌ No message selected")
            return False
            
        # Restore original value
        os.environ['USE_TELEGRAM_USER_API'] = original_value
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Improved Bot API Test")
    print("=" * 60)
    
    print("🎯 Goal: Verify the improved Bot API can get recent messages")
    print("Expected recent messages: #22943, #22942, #22941, etc.")
    print("Previous issue: Was getting #22893, #22892, etc. (50 messages behind)")
    print()
    
    # Test 1: Improved Bot API
    test1_result = await test_improved_bot_api()
    
    # Test 2: Message selection
    test2_result = await test_message_selection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    print(f"Improved Bot API: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Message Selection: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 SUCCESS! The improved Bot API is working!")
        print("✅ Getting recent messages (much closer to current)")
        print("✅ Message selection working correctly")
        print("✅ The 50-message gap issue is significantly reduced!")
        
        print("\n📝 What was improved:")
        print("- Increased update limit to 500")
        print("- Better message filtering and sorting")
        print("- Improved polling logic")
        print("- Even without User API, Bot API is much better now")
    else:
        print("\n⚠️  Some tests failed.")
        
        if not test1_result:
            print("🔧 Bot API still needs improvement")
        if not test2_result:
            print("🔧 Message selection logic needs work")
    
    print("\n💡 RECOMMENDATION:")
    if test1_result:
        print("✅ The improved Bot API is working well enough!")
        print("✅ You can use the app now with much better message selection")
        print("🔧 User API can be added later for even better results")
    else:
        print("🔧 Consider setting up User API for best results")
        print("🔧 Or investigate Bot API connection issues")

if __name__ == "__main__":
    asyncio.run(main())
