#!/usr/bin/env python3
"""
Full integration test for both Instagram and YouTube posting
"""
import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import Config
from src.main_app import LinkInstaApp
from src.message_processor import ProcessedMessage, TelegramMessage
from src.utils.logger import logger

def create_test_message():
    """Create a test message for processing"""
    # Create a mock telegram message
    class MockTelegramMessage:
        def __init__(self):
            self.message_id = 99999
            self.text = "This is a test message for our dual-platform posting system! 🚀"
            self.text_content = "This is a test message for our dual-platform posting system! 🚀"
            self.content_type = "text_only"
            self.media_files = []
            self.is_v2ray_config = False
    
    # Create processed message
    telegram_msg = MockTelegramMessage()
    processed_msg = ProcessedMessage(telegram_msg)
    
    # Find an existing video to use for testing
    output_dir = Path("output_videos")
    video_files = list(output_dir.glob("*.mp4"))
    
    if video_files:
        processed_msg.output_video_path = video_files[0]
        processed_msg.video_generated = True
        print(f"📹 Using test video: {video_files[0].name}")
    else:
        print("❌ No video files found for testing")
        return None
    
    return processed_msg

async def test_dual_platform_posting():
    """Test posting to both Instagram and YouTube"""
    print("🚀 Testing Dual Platform Posting...")
    print("=" * 50)
    
    # Create app instance
    app = LinkInstaApp()
    
    # Create test message
    test_message = create_test_message()
    if not test_message:
        return False
    
    print(f"📝 Test Caption: {test_message.text_content}")
    print(f"📹 Test Video: {test_message.output_video_path}")
    print(f"📱 Instagram Enabled: {Config.ENABLE_INSTAGRAM_POSTING}")
    print(f"📺 YouTube Enabled: {Config.ENABLE_YOUTUBE_POSTING}")
    print(f"🧪 Test Mode: {Config.TEST_MODE}")
    
    # Test the posting method
    try:
        print("\n🎬 Starting dual platform posting test...")
        success = await app.post_instantly_to_platforms(test_message)
        
        if success:
            print("✅ Dual platform posting test successful!")
            return True
        else:
            print("❌ Dual platform posting test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during posting test: {e}")
        return False

def test_instagram_caption_farsi():
    """Test Instagram caption with Farsi text"""
    print("\n📱 Testing Instagram Caption with Farsi...")
    
    from src.instagram_client import InstagramClient
    
    instagram_client = InstagramClient()
    test_text = "Testing our automation system with dual platform support"
    caption = instagram_client._prepare_caption(test_text)
    
    print("Generated Caption:")
    print("-" * 30)
    print(caption)
    print("-" * 30)
    
    # Check for Farsi text
    farsi_text = "این محتوا بر اساس توییت‌های شما ساخته شده است"
    if farsi_text in caption:
        print("✅ Farsi sentence found in caption!")
        return True
    else:
        print("❌ Farsi sentence missing from caption")
        return False

def test_youtube_title_generation():
    """Test YouTube title generation"""
    print("\n📺 Testing YouTube Title Generation...")
    
    test_caption = "This is a long test message that should be truncated properly when used as a YouTube title"
    expected_title = f"LinkInsta Content - {test_caption[:50]}..."
    
    print(f"Original Caption: {test_caption}")
    print(f"Generated Title: {expected_title}")
    
    if len(expected_title) <= 100:  # YouTube title limit
        print("✅ Title length is within YouTube limits")
        return True
    else:
        print("❌ Title too long for YouTube")
        return False

async def main():
    """Main test function"""
    print("🎯 LinkInsta Full Integration Test")
    print("=" * 50)
    
    tests = [
        ("Instagram Caption with Farsi", test_instagram_caption_farsi),
        ("YouTube Title Generation", test_youtube_title_generation),
        ("Dual Platform Posting", test_dual_platform_posting),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FINAL TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("🚀 Your LinkInsta app is ready for dual-platform posting!")
        print("\n📋 What's working:")
        print("✅ Instagram posting with Farsi sentence")
        print("✅ YouTube Shorts uploading")
        print("✅ Dual platform posting")
        print("✅ Error handling and notifications")
        
        print("\n🎬 To start using:")
        print("1. Run: python main.py")
        print("2. Add content to your Telegram channel")
        print("3. Watch videos get posted to both platforms!")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
