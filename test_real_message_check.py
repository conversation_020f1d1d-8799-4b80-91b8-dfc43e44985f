#!/usr/bin/env python3
"""
Test to check what messages we're actually getting vs what we should get
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def check_actual_messages():
    """Check what messages we're actually getting"""
    print("🔍 Checking Actual Message Retrieval")
    print("=" * 50)
    
    try:
        from src.telegram_client import TelegramClient
        
        # Create Telegram client
        telegram_client = TelegramClient()
        
        if not await telegram_client.initialize():
            print("❌ Failed to initialize Telegram client")
            return False
        
        print("✅ Telegram client initialized")
        
        # Get messages in polling mode
        print("\n📡 Getting messages in polling mode...")
        messages = await telegram_client.get_recent_messages(limit=10, for_polling=True)
        
        if not messages:
            print("❌ No messages retrieved")
            return False
        
        print(f"📊 Retrieved {len(messages)} messages")
        
        # Expected recent message IDs based on your info
        expected_recent = [22943, 22942, 22941, 22940, 22939, 22938, 22937, 22936, 22935, 22934]
        
        print(f"\n📋 Expected recent messages: {expected_recent}")
        
        actual_ids = [msg.message_id for msg in messages]
        print(f"📋 Actually got messages: {actual_ids}")
        
        # Check if we're getting the right messages
        overlap = set(expected_recent) & set(actual_ids)
        print(f"\n📊 Overlap with expected: {len(overlap)}/10 messages")
        
        if len(overlap) >= 7:  # At least 70% overlap
            print("✅ Getting mostly correct recent messages")
        else:
            print("❌ Still getting wrong messages!")
            
        # Show message details
        print(f"\n📝 Message Details:")
        tehran_tz = pytz.timezone('Asia/Tehran')
        now_tehran = datetime.now(tehran_tz)
        
        for i, msg in enumerate(messages):
            # Convert UTC to Tehran time
            msg_tehran = msg.date.astimezone(tehran_tz)
            age = now_tehran - msg_tehran
            age_hours = age.total_seconds() / 3600
            
            reaction_count = getattr(msg, 'reaction_count', 0)
            
            print(f"  {i+1}. Message #{msg.message_id}")
            print(f"     Tehran time: {msg_tehran.strftime('%H:%M %d/%m')}")
            print(f"     Age: {age_hours:.1f} hours")
            print(f"     Reactions: {reaction_count}")
            print(f"     Text: {(msg.text or msg.caption or 'No text')[:50]}...")
            
            # Check if this matches your expected messages
            if msg.message_id in expected_recent:
                print(f"     ✅ CORRECT - This is a recent message")
            else:
                print(f"     ❌ WRONG - This is not in the recent 10")
            print()
        
        return len(overlap) >= 7
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        try:
            await telegram_client.close()
        except:
            pass

async def test_message_selection():
    """Test what message gets selected"""
    print("\n🎯 Testing Message Selection")
    print("=" * 50)
    
    try:
        from src.message_processor import MessageProcessor
        
        processor = MessageProcessor()
        
        # Test instant posting selection
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected = processed_messages[0]
            msg_id = selected.telegram_message.message_id
            reactions = getattr(selected.telegram_message, 'reaction_count', 0)
            
            print(f"✅ Selected message: #{msg_id}")
            print(f"🔥 Reactions: {reactions}")
            print(f"📝 Content: {selected.text_content[:100]}...")
            
            # Check if this is one of the recent messages you mentioned
            recent_with_reactions = [22938, 22937, 22936]  # The ones you said have most reactions
            
            if msg_id in recent_with_reactions:
                print(f"✅ PERFECT! Selected one of the recent high-reaction messages")
                return True
            elif msg_id >= 22934:  # At least in the recent 10
                print(f"✅ GOOD! Selected a recent message (though maybe not highest reactions)")
                return True
            else:
                print(f"❌ WRONG! Selected an old message")
                return False
        else:
            print("❌ No message selected")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def main():
    """Main test"""
    print("🚀 Real Message Check Test")
    print("=" * 60)
    
    print("Based on your info, the recent messages should be:")
    print("- #22943 (10:39 AM Tehran) - 1 reaction")
    print("- #22942, #22941 - 1 reaction each")
    print("- #22938, #22937, #22936 - Most reactions in last 10")
    print()
    
    # Test 1: Check message retrieval
    retrieval_ok = await check_actual_messages()
    
    # Test 2: Check selection
    selection_ok = await test_message_selection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 RESULTS")
    print("=" * 60)
    
    print(f"Message Retrieval: {'✅ PASS' if retrieval_ok else '❌ FAIL'}")
    print(f"Message Selection: {'✅ PASS' if selection_ok else '❌ FAIL'}")
    
    if not retrieval_ok:
        print("\n🔧 ISSUE: Still not getting the most recent messages")
        print("Need to fix the Telegram API call further")
    elif not selection_ok:
        print("\n🔧 ISSUE: Getting recent messages but selecting wrong one")
        print("Need to fix the selection logic")
    else:
        print("\n🎉 SUCCESS: Everything working correctly!")

if __name__ == "__main__":
    asyncio.run(main())
