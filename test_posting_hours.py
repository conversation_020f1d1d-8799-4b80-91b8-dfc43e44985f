#!/usr/bin/env python3
"""
Test script to demonstrate posting hours behavior
"""
import sys
import os
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import Config

def test_current_time_check():
    """Test if current time is within posting hours"""
    print("🕐 Current Time Check")
    print("=" * 40)
    
    # Get current time in Tehran
    tehran_tz = pytz.timezone('Asia/Tehran')
    now_tehran = datetime.now(tehran_tz)
    current_hour = now_tehran.hour
    
    posting_start = Config.POSTING_START_HOUR
    posting_end = Config.POSTING_END_HOUR
    
    print(f"📍 Current Tehran time: {now_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"⏰ Current hour: {current_hour}")
    print(f"🕘 Posting window: {posting_start}:00 - {posting_end}:00")
    print(f"🔧 Respect posting hours in instant mode: {Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE}")
    
    # Check if within posting hours
    within_hours = posting_start <= current_hour < posting_end
    
    if within_hours:
        print("✅ WITHIN posting hours - instant posting would proceed")
    else:
        print("❌ OUTSIDE posting hours - instant posting would be blocked")
        
        # Calculate next posting window
        if current_hour < posting_start:
            hours_until_next = posting_start - current_hour
            print(f"⏳ Next posting window starts in {hours_until_next} hours")
        else:
            hours_until_next = (24 - current_hour) + posting_start
            print(f"⏳ Next posting window starts in {hours_until_next} hours (tomorrow)")
    
    return within_hours

def test_different_hours():
    """Test behavior at different hours"""
    print("\n🕐 Testing Different Hours")
    print("=" * 40)
    
    test_hours = [6, 9, 12, 15, 18, 21, 24, 2]
    posting_start = Config.POSTING_START_HOUR
    posting_end = Config.POSTING_END_HOUR
    
    for hour in test_hours:
        within_hours = posting_start <= hour < posting_end
        status = "✅ ALLOW" if within_hours else "❌ BLOCK"
        print(f"Hour {hour:2d}:00 - {status}")

def show_configuration():
    """Show current configuration"""
    print("\n⚙️  Current Configuration")
    print("=" * 40)
    
    print(f"📱 Instagram posting: {Config.ENABLE_INSTAGRAM_POSTING}")
    print(f"📺 YouTube posting: {Config.ENABLE_YOUTUBE_POSTING}")
    print(f"⚡ Instant posting mode: {Config.INSTANT_POSTING_MODE}")
    print(f"🕐 Respect posting hours in instant mode: {Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE}")
    print(f"🕘 Posting start hour: {Config.POSTING_START_HOUR}")
    print(f"🕘 Posting end hour: {Config.POSTING_END_HOUR}")

def show_recommendations():
    """Show recommendations for different use cases"""
    print("\n💡 Recommendations")
    print("=" * 40)
    
    print("🔥 For maximum reach (current setting):")
    print("   RESPECT_POSTING_HOURS_IN_INSTANT_MODE=false")
    print("   → Posts immediately 24/7 when content is detected")
    print("   → Best for viral content and immediate engagement")
    
    print("\n📅 For professional scheduling:")
    print("   RESPECT_POSTING_HOURS_IN_INSTANT_MODE=true")
    print("   → Only posts during business hours (9 AM - 12 midnight)")
    print("   → Content outside hours is skipped (could be queued)")
    
    print("\n⚖️  Hybrid approach:")
    print("   Use scheduled mode instead of instant mode")
    print("   → Automatically queues content for optimal times")
    print("   → Best of both worlds")

def main():
    """Main function"""
    print("🕐 LinkInsta Posting Hours Test")
    print("=" * 50)
    
    show_configuration()
    test_current_time_check()
    test_different_hours()
    show_recommendations()
    
    print("\n" + "=" * 50)
    print("📝 Summary:")
    
    if Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE:
        print("✅ Your app WILL respect posting hours in instant mode")
        print("⏰ Content outside 9 AM - 12 midnight (Tehran) will be skipped")
    else:
        print("⚡ Your app will post immediately 24/7")
        print("🔥 Maximum engagement but no time restrictions")
    
    print("\n🔧 To change this behavior:")
    print("Edit .env file: RESPECT_POSTING_HOURS_IN_INSTANT_MODE=true/false")

if __name__ == "__main__":
    main()
