"""
Configuration module for LinkInsta automation app
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Application configuration"""
    
    # Base paths
    BASE_DIR = Path(__file__).parent.parent
    SRC_DIR = BASE_DIR / "src"
    AUDIO_DIR = BASE_DIR / "audio"
    FONTS_DIR = BASE_DIR / "fonts"
    OUTPUT_DIR = BASE_DIR / "output_videos"
    TEMP_DIR = BASE_DIR / "temp"
    LOGS_DIR = BASE_DIR / "logs"
    
    # Telegram settings
    TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
    TELEGRAM_CHAT_ID = int(os.getenv("TELEGRAM_CHAT_ID", "-1001069766340"))  # Channel for reading messages
    TELEGRAM_CHANNEL_USERNAME = os.getenv("TELEGRAM_CHANNEL_USERNAME", "@linkychannel")
    TELEGRAM_PERSONAL_CHAT_ID = int(os.getenv("TELEGRAM_PERSONAL_CHAT_ID", "142183523"))  # Your personal chat for videos

    # Telegram User API settings (for reaction access)
    TELEGRAM_API_ID = int(os.getenv('TELEGRAM_API_ID', 0))
    TELEGRAM_API_HASH = os.getenv('TELEGRAM_API_HASH', '')
    TELEGRAM_PHONE = os.getenv('TELEGRAM_PHONE', '')
    USE_TELEGRAM_USER_API = os.getenv('USE_TELEGRAM_USER_API', 'false').lower() == 'true'

    # Instagram settings
    INSTAGRAM_USERNAME = os.getenv("INSTAGRAM_USERNAME")
    INSTAGRAM_PASSWORD = os.getenv("INSTAGRAM_PASSWORD")
    INSTAGRAM_SESSION_FILE = BASE_DIR / os.getenv("INSTAGRAM_SESSION_FILE", "instagram_session.json")

    # YouTube settings
    YOUTUBE_CLIENT_SECRETS_FILE = BASE_DIR / os.getenv("YOUTUBE_CLIENT_SECRETS_FILE", "youtube_client_secrets.json")
    YOUTUBE_CREDENTIALS_FILE = BASE_DIR / os.getenv("YOUTUBE_CREDENTIALS_FILE", "youtube_credentials.json")
    YOUTUBE_CHANNEL_ID = os.getenv("YOUTUBE_CHANNEL_ID")
    YOUTUBE_DEFAULT_TITLE = os.getenv("YOUTUBE_DEFAULT_TITLE", "LinkInsta Content")
    YOUTUBE_DEFAULT_DESCRIPTION = os.getenv("YOUTUBE_DEFAULT_DESCRIPTION", "Content generated by LinkInsta automation")
    YOUTUBE_DEFAULT_TAGS = os.getenv("YOUTUBE_DEFAULT_TAGS", "linky,persian,farsi,content,shorts").split(",")
    YOUTUBE_PRIVACY_STATUS = os.getenv("YOUTUBE_PRIVACY_STATUS", "public")  # public, private, unlisted
    
    # Application settings
    POLL_INTERVAL_MINUTES = int(os.getenv("POLL_INTERVAL_MINUTES", "2"))  # Changed to 2 minutes for testing
    MAX_MESSAGES_TO_PROCESS = int(os.getenv("MAX_MESSAGES_TO_PROCESS", "5"))
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    DEMO_MODE = os.getenv("DEMO_MODE", "false").lower() == "true"

    # Testing settings
    TEST_MODE = os.getenv("TEST_MODE", "true").lower() == "true"  # Prevent actual Instagram posting during testing

    # Feature toggles
    ENABLE_INSTAGRAM_POSTING = os.getenv("ENABLE_INSTAGRAM_POSTING", "false").lower() == "true"
    ENABLE_YOUTUBE_POSTING = os.getenv("ENABLE_YOUTUBE_POSTING", "false").lower() == "true"
    ENABLE_TELEGRAM_VIDEO_SENDING = os.getenv("ENABLE_TELEGRAM_VIDEO_SENDING", "false").lower() == "true"
    INSTANT_POSTING_MODE = os.getenv("INSTANT_POSTING_MODE", "false").lower() == "true"
    RESPECT_POSTING_HOURS_IN_INSTANT_MODE = os.getenv("RESPECT_POSTING_HOURS_IN_INSTANT_MODE", "false").lower() == "true"
    
    # Video settings
    VIDEO_WIDTH = int(os.getenv("VIDEO_WIDTH", "1080"))
    VIDEO_HEIGHT = int(os.getenv("VIDEO_HEIGHT", "1920"))
    VIDEO_DURATION_SECONDS = int(os.getenv("VIDEO_DURATION_SECONDS", "10"))
    FPS = int(os.getenv("FPS", "30"))
    
    # Font settings
    FONT_PATH = BASE_DIR / os.getenv("FONT_PATH", "fonts/Vazir-Regular.ttf")
    FONT_SIZE = int(os.getenv("FONT_SIZE", "48"))
    TEXT_COLOR = os.getenv("TEXT_COLOR", "white")
    BACKGROUND_COLOR = os.getenv("BACKGROUND_COLOR", "black")
    
    # Scheduling settings
    MIN_POST_INTERVAL_HOURS = int(os.getenv("MIN_POST_INTERVAL_HOURS", "2"))
    MAX_POST_INTERVAL_HOURS = int(os.getenv("MAX_POST_INTERVAL_HOURS", "8"))
    POSTING_START_HOUR = int(os.getenv("POSTING_START_HOUR", "8"))
    POSTING_END_HOUR = int(os.getenv("POSTING_END_HOUR", "22"))

    # Aliases for backward compatibility
    POSTING_HOURS_START = POSTING_START_HOUR
    POSTING_HOURS_END = POSTING_END_HOUR

    # Schedule management
    RESET_SCHEDULE = os.getenv("RESET_SCHEDULE", "false").lower() == "true"
    MAX_POSTS_TO_SCHEDULE_AT_ONCE = int(os.getenv("MAX_POSTS_TO_SCHEDULE_AT_ONCE", "10"))

    # Message filtering
    REQUIRE_REACTIONS = os.getenv("REQUIRE_REACTIONS", "true").lower() == "true"
    FILTER_FREEV2RAY = os.getenv("FILTER_FREEV2RAY", "true").lower() == "true"
    
    # V2ray config detection
    V2RAY_CONFIG_START_TEXT = "کانفیگ رایگان شما"
    
    # Instagram branding
    INSTAGRAM_HANDLE = "@linkychannell"
    CHANNEL_NAME = "linkychannell"
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories if they don't exist"""
        for directory in [cls.AUDIO_DIR, cls.FONTS_DIR, cls.OUTPUT_DIR, 
                         cls.TEMP_DIR, cls.LOGS_DIR]:
            directory.mkdir(exist_ok=True)
    
    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        required_vars = [
            ("TELEGRAM_BOT_TOKEN", cls.TELEGRAM_BOT_TOKEN),
            ("INSTAGRAM_USERNAME", cls.INSTAGRAM_USERNAME),
            ("INSTAGRAM_PASSWORD", cls.INSTAGRAM_PASSWORD),
        ]
        
        missing_vars = [var_name for var_name, var_value in required_vars if not var_value]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True
