#!/usr/bin/env python3
"""
Comprehensive test of the app to check for errors and show message selection
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_app_initialization():
    """Test app initialization for errors"""
    print("🔧 Testing App Initialization")
    print("=" * 50)
    
    try:
        from src.main_app import LinkInstaApp
        
        print("🔄 Creating LinkInsta app instance...")
        app = LinkInstaApp()
        print("✅ App instance created successfully")
        
        # Test individual components
        print("\n📱 Testing Instagram client...")
        if hasattr(app, 'instagram_client'):
            print("✅ Instagram client initialized")
        else:
            print("❌ Instagram client missing")
            
        print("📺 Testing YouTube client...")
        if hasattr(app, 'youtube_client'):
            print("✅ YouTube client initialized")
        else:
            print("❌ YouTube client missing")
            
        print("📡 Testing message processor...")
        if hasattr(app, 'message_processor'):
            print("✅ Message processor initialized")
        else:
            print("❌ Message processor missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during app initialization: {e}")
        return False

async def test_message_retrieval_and_selection():
    """Test message retrieval and selection with your recent like"""
    print("\n📡 Testing Message Retrieval and Selection")
    print("=" * 60)
    
    try:
        from src.message_processor import MessageProcessor
        
        # Create message processor
        processor = MessageProcessor()
        
        print("🔄 Initializing Telegram client...")
        if not await processor.telegram_client.initialize():
            print("❌ Failed to initialize Telegram client")
            return False
        
        print("✅ Telegram client initialized")
        
        # Get recent messages to see your like
        print("\n📡 Getting recent messages (including your recent like)...")
        messages = await processor.telegram_client.get_recent_messages(limit=15, for_polling=True)
        
        if not messages:
            print("❌ No messages retrieved")
            return False
        
        print(f"✅ Retrieved {len(messages)} recent messages")
        
        # Show all messages with their reaction counts
        tehran_tz = pytz.timezone('Asia/Tehran')
        now_tehran = datetime.now(tehran_tz)
        
        print(f"\n📊 All Recent Messages (including your like):")
        print("=" * 80)
        
        for i, msg in enumerate(messages):
            msg_tehran = msg.date.astimezone(tehran_tz)
            age = now_tehran - msg_tehran
            age_hours = age.total_seconds() / 3600
            
            reaction_count = getattr(msg, 'reaction_count', 0)
            
            print(f"  {i+1:2d}. Message #{msg.message_id}")
            print(f"      Time: {msg_tehran.strftime('%H:%M %d/%m')} ({age_hours:.1f}h ago)")
            print(f"      Reactions: {reaction_count}")
            print(f"      Text: {(msg.text or msg.caption or 'No text')[:70]}...")
            
            if reaction_count > 0:
                print(f"      🔥 HAS REACTIONS - Potential selection candidate!")
            print()
        
        # Now test the actual selection process
        print("🎯 Testing Message Selection Process...")
        print("=" * 50)
        
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected = processed_messages[0]
            msg_id = selected.telegram_message.message_id
            reactions = getattr(selected.telegram_message, 'reaction_count', 0)
            
            print(f"✅ SELECTED MESSAGE:")
            print(f"📱 Message ID: #{msg_id}")
            print(f"🔥 Reactions: {reactions}")
            print(f"⏰ Time: {selected.telegram_message.date.astimezone(tehran_tz).strftime('%H:%M %d/%m')}")
            print(f"📝 Content: {selected.text_content}")
            print()
            
            # Check if this is the message you liked
            if reactions > 0:
                print("🎉 EXCELLENT! Selected a message with reactions!")
                print("👍 This might be the message you just liked!")
                print("✅ Reaction-based selection is working perfectly!")
            else:
                print("📝 Selected message has 0 reactions")
                print("💡 This means either:")
                print("   - No messages have reactions yet")
                print("   - Your like hasn't been detected by the bot API")
            
            return selected
        else:
            print("❌ No message selected")
            return None
            
    except Exception as e:
        print(f"❌ Error during message retrieval/selection: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        try:
            await processor.telegram_client.close()
        except:
            pass

async def test_video_generation_readiness():
    """Test if video generation components are ready"""
    print("\n🎬 Testing Video Generation Readiness")
    print("=" * 50)
    
    try:
        from src.video_generators import TextVideoGenerator, TextImageVideoGenerator
        from src.audio_manager import AudioManager
        
        print("🔄 Testing video generators...")
        
        # Test text video generator
        text_gen = TextVideoGenerator()
        print("✅ Text video generator initialized")
        
        # Test text+image video generator
        text_image_gen = TextImageVideoGenerator()
        print("✅ Text+image video generator initialized")
        
        # Test audio manager
        audio_mgr = AudioManager()
        print("✅ Audio manager initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in video generation components: {e}")
        return False

async def test_posting_clients():
    """Test Instagram and YouTube clients"""
    print("\n📱 Testing Posting Clients")
    print("=" * 50)
    
    try:
        from src.instagram_client import InstagramClient
        from src.youtube_client import YouTubeClient
        from src.config import Config
        
        print("📱 Testing Instagram client...")
        instagram_client = InstagramClient()
        print("✅ Instagram client created")
        
        print("📺 Testing YouTube client...")
        youtube_client = YouTubeClient()
        print("✅ YouTube client created")
        
        print(f"🔧 Instagram posting enabled: {Config.ENABLE_INSTAGRAM_POSTING}")
        print(f"🔧 YouTube posting enabled: {Config.ENABLE_YOUTUBE_POSTING}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in posting clients: {e}")
        return False

async def main():
    """Main comprehensive test"""
    print("🚀 LinkInsta App Comprehensive Test")
    print("=" * 70)
    
    print("🎯 Goals:")
    print("1. Check for any errors in the app")
    print("2. Show you the selected message for approval")
    print("3. Verify your recent like is detected")
    print()
    
    # Run all tests
    test_results = []
    
    # Test 1: App initialization
    result1 = await test_app_initialization()
    test_results.append(("App Initialization", result1))
    
    # Test 2: Message retrieval and selection (most important)
    selected_message = await test_message_retrieval_and_selection()
    test_results.append(("Message Selection", selected_message is not None))
    
    # Test 3: Video generation readiness
    result3 = await test_video_generation_readiness()
    test_results.append(("Video Generation", result3))
    
    # Test 4: Posting clients
    result4 = await test_posting_clients()
    test_results.append(("Posting Clients", result4))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    print(f"\n🎯 Overall Status: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if selected_message:
        print(f"\n👍 SELECTED MESSAGE FOR YOUR APPROVAL:")
        print(f"📱 Message ID: #{selected_message.telegram_message.message_id}")
        print(f"🔥 Reactions: {getattr(selected_message.telegram_message, 'reaction_count', 0)}")
        print(f"📝 Content: {selected_message.text_content[:200]}...")
        print(f"\n❓ Do you approve this message selection?")
    
    if all_passed:
        print(f"\n🎉 Your LinkInsta app is ready to run without errors!")
        print(f"✅ All components working correctly")
        print(f"✅ Message selection functioning properly")
        print(f"✅ Ready for dual-platform posting")

if __name__ == "__main__":
    asyncio.run(main())
