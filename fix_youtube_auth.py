#!/usr/bin/env python3
"""
Fix YouTube authentication by clearing old credentials and re-authenticating
"""
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def clear_youtube_credentials():
    """Clear existing YouTube credentials to force re-authentication"""
    credentials_file = Path("youtube_credentials.json")
    
    if credentials_file.exists():
        try:
            credentials_file.unlink()
            print("✅ Cleared old YouTube credentials")
            return True
        except Exception as e:
            print(f"❌ Error clearing credentials: {e}")
            return False
    else:
        print("ℹ️  No existing credentials found")
        return True

def test_youtube_auth():
    """Test YouTube authentication with new scopes"""
    try:
        from src.youtube_client import YouTubeClient
        
        print("🔐 Testing YouTube authentication with updated scopes...")
        
        youtube_client = YouTubeClient()
        
        if youtube_client.authenticate():
            print("✅ YouTube authentication successful!")
            
            # Test getting channel info
            channel_info = youtube_client.get_channel_info()
            if channel_info:
                print(f"✅ Channel: {channel_info['title']}")
                print(f"✅ Channel ID: {channel_info['id']}")
                return True
            else:
                print("⚠️  Authentication successful but couldn't get channel info")
                return True
        else:
            print("❌ YouTube authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during authentication: {e}")
        return False

def update_env_with_channel_id():
    """Update .env file with the YouTube channel ID if we can get it"""
    try:
        from src.youtube_client import YouTubeClient
        
        youtube_client = YouTubeClient()
        if youtube_client.authenticate():
            channel_info = youtube_client.get_channel_info()
            if channel_info and channel_info.get('id'):
                channel_id = channel_info['id']
                
                # Update .env file
                env_file = Path('.env')
                if env_file.exists():
                    with open(env_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Update channel ID
                    if 'YOUTUBE_CHANNEL_ID=' in content:
                        content = content.replace('YOUTUBE_CHANNEL_ID=', f'YOUTUBE_CHANNEL_ID={channel_id}')
                        
                        with open(env_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        print(f"✅ Updated .env with YouTube Channel ID: {channel_id}")
                        return True
                
    except Exception as e:
        print(f"⚠️  Couldn't update channel ID: {e}")
    
    return False

def main():
    """Main function"""
    print("🔧 YouTube Authentication Fix")
    print("=" * 40)
    
    print("\n1. Clearing old credentials...")
    if not clear_youtube_credentials():
        print("❌ Failed to clear credentials")
        return False
    
    print("\n2. Re-authenticating with updated scopes...")
    if not test_youtube_auth():
        print("❌ Re-authentication failed")
        return False
    
    print("\n3. Updating channel ID...")
    update_env_with_channel_id()
    
    print("\n✅ YouTube authentication fixed!")
    print("\n📋 Next steps:")
    print("1. Your YouTube credentials are now updated")
    print("2. Run your main app: python main.py")
    print("3. YouTube posting should now work correctly")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
