#!/usr/bin/env python3
"""
Script to diagnose and fix duplicate video generation issues
"""
import sys
import json
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.message_processor import MessageProcessor
from src.utils.logger import logger

def main():
    """Main function to diagnose and fix duplicates"""
    print("=== LinkInsta Duplicate Video Fix Tool ===\n")
    
    try:
        # Initialize message processor
        processor = MessageProcessor()
        
        # Get current status
        print("1. Current Status:")
        status = processor.get_duplicate_status()
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        print("\n2. Processing Queue Analysis:")
        for i, msg in enumerate(processor.processing_queue):
            print(f"   Message {msg.telegram_message.message_id}: "
                  f"Video Generated: {msg.video_generated}, "
                  f"Content Type: {msg.content_type}")
            if i >= 10:  # Limit output
                print(f"   ... and {len(processor.processing_queue) - 10} more messages")
                break
        
        print("\n3. Telegram Client Processed Messages:")
        processed_ids = list(processor.telegram_client.processed_message_ids)
        print(f"   Total processed by Telegram client: {len(processed_ids)}")
        if processed_ids:
            print(f"   Recent IDs: {processed_ids[-5:]}")  # Show last 5
        
        # Check for potential issues
        potential_duplicates = status.get('potential_duplicates', 0)
        if potential_duplicates > 0:
            print(f"\n⚠️  WARNING: Found {potential_duplicates} potential duplicate issues!")
            print("   This means Telegram client has processed more messages than have videos generated.")
            
            response = input("\nDo you want to reset the tracking to fix duplicates? (y/N): ")
            if response.lower() == 'y':
                print("\n🔧 Resetting duplicate tracking...")
                processor.reset_duplicate_tracking()
                
                # Show new status
                print("\n✅ Reset complete! New status:")
                new_status = processor.get_duplicate_status()
                for key, value in new_status.items():
                    print(f"   {key}: {value}")
            else:
                print("\n❌ No changes made.")
        else:
            print("\n✅ No duplicate issues detected!")
        
        print("\n=== Analysis Complete ===")
        
    except Exception as e:
        logger.error(f"Error in duplicate fix tool: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
