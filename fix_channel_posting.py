#!/usr/bin/env python3
"""
Fix channel posting issue - ensure videos go to the correct Top Media channel
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from youtube_client import YouTubeClient
from video_generators.text_video_generator import TextVideoGenerator
from utils.logger import logger


def check_channel_access():
    """Check which channels we can access and post to"""
    print("🔍 Checking Channel Access...\n")
    
    try:
        topmedia_client = YouTubeClient("topmedia")
        
        if not topmedia_client.authenticate():
            print("❌ Authentication failed")
            return False
        
        # Check all channels the user can manage
        print("📺 Checking manageable channels...")
        
        # Get channels where the user has content manager access
        request = topmedia_client.service.channels().list(
            part='snippet,statistics,contentDetails',
            managedByMe=True,
            maxResults=50
        )
        response = request.execute()
        
        print(f"✅ Found {len(response.get('items', []))} manageable channel(s):")
        
        target_channel_id = Config.TOPMEDIA_YOUTUBE_CHANNEL_ID
        target_found = False
        
        for i, channel in enumerate(response.get('items', []), 1):
            channel_id = channel['id']
            channel_title = channel['snippet']['title']
            
            is_target = channel_id == target_channel_id
            if is_target:
                target_found = True
            
            status = "🎯 TARGET" if is_target else "📺"
            print(f"   {status} {channel_title} ({channel_id})")
        
        if not target_found:
            print(f"\n⚠️ Target channel not in manageable list, checking direct access...")
            
            # Try direct access to target channel
            direct_request = topmedia_client.service.channels().list(
                part='snippet',
                id=target_channel_id
            )
            direct_response = direct_request.execute()
            
            if direct_response.get('items'):
                channel = direct_response['items'][0]
                print(f"✅ Can access target channel directly: {channel['snippet']['title']}")
                target_found = True
            else:
                print(f"❌ Cannot access target channel at all")
        
        return target_found
        
    except Exception as e:
        print(f"❌ Error checking channel access: {e}")
        return False


def create_test_video_with_verification():
    """Create and upload test video with channel verification"""
    print("\n🎬 Creating Test Video with Channel Verification...\n")
    
    try:
        # Generate test video
        test_text = """🔥 تست کانال تاپ مدیا - نسخه اصلاح شده 🔥

این ویدیو باید روی کانال Top Media منتشر شود.

✅ کانال هدف: @topmedii
✅ شناسه کانال: UCbaraBa9dKSYeToN4DBy1wQ
✅ برندینگ: @topmedi

#TopMedia #Test #ChannelFix"""
        
        topmedia_generator = TextVideoGenerator("topmedia")
        test_output = Config.OUTPUT_DIR / "topmedia_channel_fix_test.mp4"
        
        print(f"📹 Generating video with Top Media branding...")
        success = topmedia_generator.generate_video(test_text, test_output)
        
        if not success:
            print("❌ Video generation failed")
            return False
        
        print(f"✅ Video generated: {test_output.name}")
        
        # Upload with verification
        topmedia_client = YouTubeClient("topmedia")
        
        title = "🔥 تست کانال تاپ مدیا - Channel Fix Test"
        description = """این ویدیو برای تست صحیح بودن انتشار روی کانال Top Media است.

🎯 اهداف تست:
✅ اطمینان از انتشار روی کانال صحیح
✅ تست برندینگ @topmedi
✅ تست طراحی سیاه و سفید

📱 این محتوا بر اساس سیستم خودکار تولید شده است

#TopMedia #ChannelTest #YouTube #Shorts"""
        
        print(f"\n📤 Uploading to YouTube...")
        print(f"   Target Channel: Top Media (@topmedii)")
        print(f"   Channel ID: {Config.TOPMEDIA_YOUTUBE_CHANNEL_ID}")
        
        video_id = topmedia_client.upload_video(
            video_path=test_output,
            title=title,
            description=description,
            tags=["topmedia", "test", "channelfix", "shorts"],
            privacy_status="public"
        )
        
        if video_id:
            print(f"\n🎉 Video uploaded successfully!")
            print(f"📺 Video ID: {video_id}")
            print(f"🔗 Video URL: https://www.youtube.com/watch?v={video_id}")
            
            # Verify which channel it was posted to
            print(f"\n🔍 Verifying upload location...")
            
            video_request = topmedia_client.service.videos().list(
                part='snippet',
                id=video_id
            )
            video_response = video_request.execute()
            
            if video_response.get('items'):
                video_info = video_response['items'][0]
                uploaded_channel_id = video_info['snippet']['channelId']
                uploaded_channel_title = video_info['snippet']['channelTitle']
                
                print(f"📊 Upload Verification:")
                print(f"   Uploaded to Channel: {uploaded_channel_title}")
                print(f"   Uploaded Channel ID: {uploaded_channel_id}")
                print(f"   Target Channel ID: {Config.TOPMEDIA_YOUTUBE_CHANNEL_ID}")
                
                if uploaded_channel_id == Config.TOPMEDIA_YOUTUBE_CHANNEL_ID:
                    print(f"✅ SUCCESS: Video posted to correct channel!")
                    return True
                else:
                    print(f"❌ ERROR: Video posted to wrong channel!")
                    print(f"   Expected: {Config.TOPMEDIA_YOUTUBE_CHANNEL_ID}")
                    print(f"   Actual: {uploaded_channel_id}")
                    return False
            else:
                print(f"⚠️ Could not verify upload location")
                return True  # Assume success if we can't verify
        else:
            print(f"❌ Upload failed")
            return False
            
    except Exception as e:
        print(f"❌ Error in test: {e}")
        return False


def provide_solution():
    """Provide solution for channel posting issue"""
    print("\n💡 SOLUTION FOR CHANNEL POSTING ISSUE:\n")
    
    print("🔧 The issue is that YouTube API uploads to the authenticated user's default channel.")
    print("   Since you're a manager (not owner) of Top Media, it uploads to your personal channel.")
    print()
    print("📋 Solutions:")
    print()
    print("1. 🎯 RECOMMENDED: Use Top Media Owner's Account")
    print("   - Get OAuth credentials from the Top Media channel owner's Google account")
    print("   - This ensures uploads go directly to Top Media channel")
    print()
    print("2. 🔄 Alternative: Change Default Channel")
    print("   - In YouTube Studio, the owner can set Top Media as your default channel")
    print("   - This requires owner permissions")
    print()
    print("3. 🛠️ Technical: Use Channel Transfer")
    print("   - Transfer ownership temporarily for uploads")
    print("   - Not recommended for monetized channels")
    print()
    print("🎯 BEST APPROACH:")
    print("   Ask the Top Media channel owner to:")
    print("   1. Create OAuth credentials from their Google account")
    print("   2. Download the client secrets JSON file")
    print("   3. Share it with you for the automation system")
    print()


def main():
    """Run channel posting fix"""
    print("🚀 TOP MEDIA CHANNEL POSTING FIX\n")
    print("=" * 60)
    
    # Step 1: Check channel access
    print("STEP 1: Check Channel Access")
    access_ok = check_channel_access()
    
    if access_ok:
        # Step 2: Test upload with verification
        print("\n" + "=" * 60)
        print("STEP 2: Test Upload with Verification")
        upload_ok = create_test_video_with_verification()
        
        if upload_ok:
            print("\n🎉 SUCCESS: Channel posting is working correctly!")
        else:
            print("\n❌ Channel posting issue confirmed")
            provide_solution()
    else:
        print("\n❌ Cannot access Top Media channel")
        provide_solution()
    
    return access_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
