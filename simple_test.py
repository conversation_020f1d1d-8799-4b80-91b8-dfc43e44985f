#!/usr/bin/env python3
"""
Simple test for text-only video generation
"""
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.video_generators.text_video_generator import TextVideoGenerator
from src.config import Config

def main():
    """Test text-only video generation"""
    print("🎬 Testing improved text-only video generation...")
    
    generator = TextVideoGenerator()
    test_text = "This is a test of the improved video design! ✨🎥"
    
    output_path = Path("output_videos") / "test_improved_design.mp4"
    output_path.parent.mkdir(exist_ok=True)
    
    try:
        success = generator.generate_video(test_text, output_path)
        if success:
            print(f"✅ Video generated successfully: {output_path}")
            print("🎉 Design improvements are working!")
            return True
        else:
            print("❌ Failed to generate video")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
