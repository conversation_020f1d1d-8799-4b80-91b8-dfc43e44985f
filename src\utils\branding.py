"""
Branding utilities for different channels
"""
from pathlib import Path
from typing import Tuple, Optional
from ..config import Config


class ChannelBranding:
    """Manages branding for different channels"""
    
    def __init__(self, channel_type: str = "linky"):
        """
        Initialize branding for specific channel
        
        Args:
            channel_type: "linky" for main channel, "topmedia" for Top Media channel
        """
        self.channel_type = channel_type
        self._set_branding_config()
    
    def _set_branding_config(self):
        """Set branding configuration based on channel type"""
        if self.channel_type == "topmedia":
            # Top Media branding - Black and white theme
            self.channel_name = Config.TOPMEDIA_CHANNEL_NAME
            self.instagram_handle = Config.TOPMEDIA_INSTAGRAM_HANDLE
            self.background_color = (0, 0, 0)  # Black
            self.text_color = (255, 255, 255)  # White
            self.accent_color = (51, 51, 51)  # Dark gray
            self.border_color = (128, 128, 128)  # Gray
            self.logo_prefix = "topmedia"
        else:
            # Linky branding - Colorful theme (original)
            self.channel_name = Config.CHANNEL_NAME
            self.instagram_handle = Config.INSTAGRAM_HANDLE
            self.background_color = (21, 32, 43)  # Twitter dark blue
            self.text_color = (255, 255, 255)  # White text
            self.accent_color = (29, 161, 242)  # Twitter blue
            self.border_color = (56, 68, 77)  # Border color
            self.logo_prefix = "linky"
    
    def get_background_color(self) -> Tuple[int, int, int]:
        """Get background color for the channel"""
        return self.background_color
    
    def get_text_color(self) -> Tuple[int, int, int]:
        """Get text color for the channel"""
        return self.text_color
    
    def get_accent_color(self) -> Tuple[int, int, int]:
        """Get accent color for the channel"""
        return self.accent_color
    
    def get_border_color(self) -> Tuple[int, int, int]:
        """Get border color for the channel"""
        return self.border_color
    
    def get_channel_name(self) -> str:
        """Get channel name"""
        return self.channel_name
    
    def get_instagram_handle(self) -> str:
        """Get Instagram handle"""
        return self.instagram_handle
    
    def get_brand_text(self) -> str:
        """Get brand text for display"""
        return f"@{self.channel_name}"
    
    def get_logo_path(self, size: str = "small") -> Optional[Path]:
        """
        Get logo path for the channel
        
        Args:
            size: Logo size ("small", "medium", "large")
            
        Returns:
            Path to logo file or None if not found
        """
        try:
            logo_path = Config.BASE_DIR / "assets" / "logos" / f"{self.logo_prefix}_logo_{size}.png"
            if logo_path.exists():
                return logo_path
            
            # Fallback to generic logo if channel-specific doesn't exist
            fallback_path = Config.BASE_DIR / "assets" / "logos" / f"logo_{size}.png"
            if fallback_path.exists():
                return fallback_path
                
        except Exception:
            pass
        return None
    
    def get_gradient_colors(self) -> Tuple[Tuple[int, int, int], Tuple[int, int, int]]:
        """
        Get gradient colors for backgrounds
        
        Returns:
            Tuple of (start_color, end_color)
        """
        if self.channel_type == "topmedia":
            # Black to dark gray gradient
            return (0, 0, 0), (32, 32, 32)
        else:
            # Twitter-like gradient
            return self.background_color, (self.background_color[0] + 10, 
                                         self.background_color[1] + 10, 
                                         self.background_color[2] + 10)
    
    def get_social_icons_colors(self) -> list:
        """Get colors for social media icons"""
        if self.channel_type == "topmedia":
            # Monochrome theme
            return [(200, 200, 200), (160, 160, 160), (120, 120, 120)]
        else:
            # Colorful theme (original)
            return [(255, 91, 109), (29, 161, 242), (23, 191, 99)]  # Like, retweet, share colors
