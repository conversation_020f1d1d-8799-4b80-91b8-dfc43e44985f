#!/usr/bin/env python3
"""
Test script to verify Instagram session handling fixes
"""
import sys
import json
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.instagram_client import InstagramClient
from src.utils.logger import logger
import logging

def test_session_invalidation():
    """Test session invalidation handling"""
    print("=" * 60)
    print("Testing Instagram Session Invalidation Handling")
    print("=" * 60)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    client = InstagramClient()
    
    # Test 1: Normal login
    print("\n1. Testing normal login...")
    success = client.login()
    print(f"   Login successful: {success}")
    
    if not success:
        print("   ❌ Login failed, cannot continue tests")
        return False
    
    # Test 2: Simulate session invalidation by corrupting session file
    print("\n2. Testing session invalidation handling...")
    
    if client.session_file.exists():
        # Backup original session
        backup_file = client.session_file.with_suffix('.backup')
        client.session_file.rename(backup_file)
        
        # Create corrupted session file
        corrupted_session = {
            "message": "user_has_logged_out",
            "logout_reason": 9,
            "status": "fail"
        }
        
        with open(client.session_file, 'w') as f:
            json.dump(corrupted_session, f)
        
        print("   Created corrupted session file")
        
        # Test login with corrupted session
        client2 = InstagramClient()
        success2 = client2.login()
        print(f"   Login with corrupted session: {success2}")
        
        # Restore original session
        if backup_file.exists():
            backup_file.rename(client.session_file)
            print("   Restored original session file")
    
    # Test 3: Verify session validation works
    print("\n3. Testing session validation...")
    try:
        timeline = client.client.get_timeline_feed()
        print("   ✅ Session validation successful")
    except Exception as e:
        print(f"   ❌ Session validation failed: {e}")
    
    print("\n" + "=" * 60)
    print("Session handling tests completed")
    print("=" * 60)
    
    return True

def test_posting_pipeline():
    """Test the complete posting pipeline"""
    print("\n" + "=" * 60)
    print("Testing Complete Posting Pipeline")
    print("=" * 60)
    
    from src.scheduler import PostScheduler
    
    # Test scheduler execution
    print("\n1. Testing scheduler execution...")
    scheduler = PostScheduler()
    print(f"   Loaded {len(scheduler.scheduled_posts)} scheduled posts")
    
    # Check if any posts should be executed now
    from datetime import datetime
    import pytz
    
    now_utc = datetime.utcnow()
    now_tehran = pytz.UTC.localize(now_utc).astimezone(scheduler.tehran_tz)
    
    print(f"   Current Tehran time: {now_tehran}")
    print(f"   In posting window: {scheduler.posting_start_hour <= now_tehran.hour < scheduler.posting_end_hour}")
    
    # Test execution
    print("\n2. Running scheduler check...")
    scheduler.check_and_execute_posts()
    
    print("\n" + "=" * 60)
    print("Posting pipeline tests completed")
    print("=" * 60)

if __name__ == "__main__":
    print("🔧 Instagram Posting Fix Verification")
    print("=====================================")
    
    try:
        # Test session handling
        test_session_invalidation()
        
        # Test posting pipeline
        test_posting_pipeline()
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
