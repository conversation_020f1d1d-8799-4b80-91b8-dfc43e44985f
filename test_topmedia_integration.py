#!/usr/bin/env python3
"""
Test script for Top Media YouTube channel integration
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from utils.branding import ChannelBranding
from video_generators.text_video_generator import TextVideoGenerator
from youtube_client import YouTubeClient
from utils.logger import logger


def test_branding_configuration():
    """Test branding configuration for both channels"""
    print("🎨 Testing Branding Configuration...")
    
    # Test Linky branding
    linky_branding = ChannelBranding("linky")
    print(f"✅ Linky Channel: {linky_branding.get_channel_name()}")
    print(f"   Handle: {linky_branding.get_instagram_handle()}")
    print(f"   Background: {linky_branding.get_background_color()}")
    print(f"   Accent: {linky_branding.get_accent_color()}")
    
    # Test Top Media branding
    topmedia_branding = ChannelBranding("topmedia")
    print(f"✅ Top Media Channel: {topmedia_branding.get_channel_name()}")
    print(f"   Handle: {topmedia_branding.get_instagram_handle()}")
    print(f"   Background: {topmedia_branding.get_background_color()}")
    print(f"   Accent: {topmedia_branding.get_accent_color()}")
    
    return True


def test_video_generation():
    """Test video generation for both channels"""
    print("\n📹 Testing Video Generation...")
    
    test_text = "این یک تست برای کانال Top Media است. محتوای جدید و جذاب برای مخاطبان ما."
    
    try:
        # Test Linky video generation
        print("Generating Linky video...")
        linky_generator = TextVideoGenerator("linky")
        linky_output = Config.OUTPUT_DIR / "test_linky_video.mp4"
        linky_success = linky_generator.generate_video(test_text, linky_output)
        
        if linky_success:
            print(f"✅ Linky video generated: {linky_output}")
        else:
            print("❌ Linky video generation failed")
        
        # Test Top Media video generation
        print("Generating Top Media video...")
        topmedia_generator = TextVideoGenerator("topmedia")
        topmedia_output = Config.OUTPUT_DIR / "test_topmedia_video.mp4"
        topmedia_success = topmedia_generator.generate_video(test_text, topmedia_output)
        
        if topmedia_success:
            print(f"✅ Top Media video generated: {topmedia_output}")
        else:
            print("❌ Top Media video generation failed")
        
        return linky_success and topmedia_success
        
    except Exception as e:
        print(f"❌ Video generation error: {e}")
        return False


def test_youtube_client_configuration():
    """Test YouTube client configuration for both channels"""
    print("\n🔧 Testing YouTube Client Configuration...")
    
    try:
        # Test Linky YouTube client
        linky_client = YouTubeClient("linky")
        print(f"✅ Linky YouTube Client initialized")
        print(f"   Channel: {linky_client.channel_name}")
        print(f"   Credentials file: {linky_client.credentials_file}")
        
        # Test Top Media YouTube client
        topmedia_client = YouTubeClient("topmedia")
        print(f"✅ Top Media YouTube Client initialized")
        print(f"   Channel: {topmedia_client.channel_name}")
        print(f"   Credentials file: {topmedia_client.credentials_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ YouTube client configuration error: {e}")
        return False


def test_configuration_values():
    """Test configuration values"""
    print("\n⚙️ Testing Configuration Values...")
    
    print(f"✅ Linky YouTube enabled: {Config.ENABLE_YOUTUBE_POSTING}")
    print(f"✅ Top Media YouTube enabled: {Config.ENABLE_TOPMEDIA_YOUTUBE_POSTING}")
    print(f"✅ Top Media channel name: {Config.TOPMEDIA_CHANNEL_NAME}")
    print(f"✅ Top Media handle: {Config.TOPMEDIA_INSTAGRAM_HANDLE}")
    print(f"✅ Top Media credentials file: {Config.TOPMEDIA_YOUTUBE_CREDENTIALS_FILE}")
    
    return True


def main():
    """Run all tests"""
    print("🚀 Starting Top Media Integration Tests...\n")
    
    # Create necessary directories
    Config.create_directories()
    
    tests = [
        ("Configuration Values", test_configuration_values),
        ("Branding Configuration", test_branding_configuration),
        ("YouTube Client Configuration", test_youtube_client_configuration),
        ("Video Generation", test_video_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Top Media integration is ready.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
