#!/usr/bin/env python3
"""
Test script to verify video generation improvements
"""
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.video_generators.text_video_generator import TextVideoGenerator
from src.video_generators.text_image_video_generator import TextImageVideoGenerator
from src.video_generators.video_text_generator import VideoTextGenerator
from src.config import Config
from src.utils.logger import logger
from PIL import Image

def test_text_only_video():
    """Test text-only video generation with improvements"""
    print("🎬 Testing text-only video generation...")
    
    generator = TextVideoGenerator()
    test_text = "این یک متن تست فارسی است که برای بررسی بهبودهای طراحی ویدیو استفاده می‌شود. 🎥✨"
    
    output_path = Path("output_videos") / "test_text_only_improved.mp4"
    output_path.parent.mkdir(exist_ok=True)
    
    try:
        success = generator.generate_video(test_text, output_path)
        if success:
            print(f"✅ Text-only video generated successfully: {output_path}")
            return True
        else:
            print("❌ Failed to generate text-only video")
            return False
    except Exception as e:
        print(f"❌ Error generating text-only video: {e}")
        return False

def test_text_image_video():
    """Test text+image video generation with improvements"""
    print("🎬 Testing text+image video generation...")
    
    generator = TextImageVideoGenerator()
    test_text = "این متن همراه با تصاویر نمایش داده می‌شود 📸"
    
    # Create some test images
    test_images = []
    for i in range(2):
        img = Image.new('RGB', (400, 300), color=(100 + i*50, 150 + i*30, 200 + i*20))
        test_images.append(img)
    
    output_path = Path("output_videos") / "test_text_image_improved.mp4"
    
    try:
        success = generator.generate_video(test_text, test_images, output_path)
        if success:
            print(f"✅ Text+image video generated successfully: {output_path}")
            return True
        else:
            print("❌ Failed to generate text+image video")
            return False
    except Exception as e:
        print(f"❌ Error generating text+image video: {e}")
        return False

def test_video_text_overlay():
    """Test video+text overlay with improvements"""
    print("🎬 Testing video+text overlay generation...")
    
    # Check if we have any sample videos
    video_files = list(Path(".").glob("*.mp4")) + list(Path(".").glob("*.avi"))
    if not video_files:
        print("⚠️ No sample video files found for testing video+text overlay")
        return False
    
    generator = VideoTextGenerator()
    test_text = "این متن روی ویدیو نمایش داده می‌شود 🎬"
    
    output_path = Path("output_videos") / "test_video_text_improved.mp4"
    
    try:
        success = generator.generate_video_with_text(
            video_path=video_files[0],
            text=test_text,
            output_path=output_path
        )
        if success:
            print(f"✅ Video+text overlay generated successfully: {output_path}")
            return True
        else:
            print("❌ Failed to generate video+text overlay")
            return False
    except Exception as e:
        print(f"❌ Error generating video+text overlay: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting video generation improvement tests...\n")
    
    results = []
    
    # Test 1: Text-only videos
    results.append(test_text_only_video())
    print()
    
    # Test 2: Text+image videos
    results.append(test_text_image_video())
    print()
    
    # Test 3: Video+text overlay
    results.append(test_video_text_overlay())
    print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("📊 Test Results Summary:")
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All video generation improvements are working correctly!")
    else:
        print(f"\n⚠️ Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
