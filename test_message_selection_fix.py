#!/usr/bin/env python3
"""
Test the message selection fix
"""
import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_message_fetching():
    """Test the improved message fetching"""
    print("🔧 Testing Message Selection Fix")
    print("=" * 50)
    
    try:
        from src.telegram_client import TelegramClient
        from src.config import Config
        
        # Create Telegram client
        telegram_client = TelegramClient()
        
        if not await telegram_client.initialize():
            print("❌ Failed to initialize Telegram client")
            return False
        
        print("✅ Telegram client initialized")
        
        # Test the improved get_recent_messages method
        print("\n📡 Testing improved message fetching...")
        
        # Get messages in polling mode (this should now get truly recent messages)
        messages = await telegram_client.get_recent_messages(limit=10, for_polling=True)
        
        if not messages:
            print("⚠️  No messages retrieved")
            return False
        
        print(f"✅ Retrieved {len(messages)} messages")
        
        # Analyze the messages
        print(f"\n📊 Message Analysis:")
        from datetime import datetime
        import pytz
        
        now = datetime.now(pytz.UTC)
        
        for i, message in enumerate(messages):
            age = now - message.date
            age_hours = age.total_seconds() / 3600
            reaction_count = getattr(message, 'reaction_count', 0)
            
            print(f"  {i+1}. Message #{message.message_id}")
            print(f"     Age: {age_hours:.1f} hours")
            print(f"     Reactions: {reaction_count}")
            print(f"     Text: {(message.text or message.caption or 'No text')[:50]}...")
            print()
        
        # Check if we're getting recent messages (within last 24 hours)
        recent_count = sum(1 for msg in messages if (now - msg.date).total_seconds() < 24 * 3600)
        
        print(f"📈 Messages within last 24 hours: {recent_count}/{len(messages)}")
        
        if recent_count >= len(messages) * 0.7:  # At least 70% should be recent
            print("✅ SUCCESS: Getting recent messages!")
            return True
        else:
            print("⚠️  Still getting old messages, may need further adjustment")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False
    
    finally:
        try:
            await telegram_client.close()
        except:
            pass

async def test_message_selection_logic():
    """Test the complete message selection logic"""
    print("\n🎯 Testing Complete Message Selection Logic")
    print("=" * 50)
    
    try:
        from src.message_processor import MessageProcessor
        
        # Create message processor
        processor = MessageProcessor()
        
        # Test the instant posting polling
        print("📡 Testing instant posting message selection...")
        
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected_msg = processed_messages[0]
            print(f"✅ Selected message #{selected_msg.telegram_message.message_id}")
            print(f"📝 Content: {selected_msg.text_content[:100]}...")
            print(f"🔥 Reactions: {getattr(selected_msg.telegram_message, 'reaction_count', 0)}")
            
            # Check message age
            from datetime import datetime
            import pytz
            now = datetime.now(pytz.UTC)
            age = now - selected_msg.telegram_message.date
            age_hours = age.total_seconds() / 3600
            print(f"⏰ Age: {age_hours:.1f} hours")
            
            if age_hours < 24:
                print("✅ SUCCESS: Selected a recent message!")
                return True
            else:
                print("⚠️  Selected message is still old")
                return False
        else:
            print("⚠️  No messages selected")
            return False
            
    except Exception as e:
        print(f"❌ Error during selection test: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 LinkInsta Message Selection Fix Test")
    print("=" * 60)
    
    # Test 1: Message fetching
    test1_result = await test_message_fetching()
    
    # Test 2: Complete selection logic
    test2_result = await test_message_selection_logic()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    print(f"✅ Message Fetching: {'PASS' if test1_result else 'FAIL'}")
    print(f"✅ Selection Logic: {'PASS' if test2_result else 'FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 SUCCESS! Message selection issue is FIXED!")
        print("✅ App will now process recent, popular messages")
        print("✅ No more selecting old messages with 0 reactions")
    else:
        print("\n⚠️  Some tests failed. The fix may need adjustment.")
    
    print("\n📝 What was fixed:")
    print("- Increased update limit from 100 to 500")
    print("- Better sorting and filtering of messages")
    print("- Improved logging for debugging")
    print("- Ensures polling mode gets truly recent messages")

if __name__ == "__main__":
    asyncio.run(main())
