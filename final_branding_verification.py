#!/usr/bin/env python3
"""
Final verification that Top Media branding is working correctly
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from video_generators.text_video_generator import TextVideoGenerator
from utils.branding import ChannelBranding


def test_branding_in_generators():
    """Test that generators actually use different branding"""
    print("🔍 Deep Testing Branding in Video Generators...\n")
    
    # Create generators
    linky_gen = TextVideoGenerator("linky")
    topmedia_gen = TextVideoGenerator("topmedia")
    
    print("📊 Linky Generator Branding:")
    print(f"   Channel Name: {linky_gen.branding.get_channel_name()}")
    print(f"   Instagram Handle: {linky_gen.branding.get_instagram_handle()}")
    print(f"   Background Color: {linky_gen.branding.get_background_color()}")
    print(f"   Accent Color: {linky_gen.branding.get_accent_color()}")
    print(f"   Brand Text: {linky_gen.branding.get_brand_text()}")
    
    print("\n📊 Top Media Generator Branding:")
    print(f"   Channel Name: {topmedia_gen.branding.get_channel_name()}")
    print(f"   Instagram Handle: {topmedia_gen.branding.get_instagram_handle()}")
    print(f"   Background Color: {topmedia_gen.branding.get_background_color()}")
    print(f"   Accent Color: {topmedia_gen.branding.get_accent_color()}")
    print(f"   Brand Text: {topmedia_gen.branding.get_brand_text()}")
    
    # Verify they're different
    different_checks = [
        ("Channel Name", linky_gen.branding.get_channel_name() != topmedia_gen.branding.get_channel_name()),
        ("Instagram Handle", linky_gen.branding.get_instagram_handle() != topmedia_gen.branding.get_instagram_handle()),
        ("Background Color", linky_gen.branding.get_background_color() != topmedia_gen.branding.get_background_color()),
        ("Accent Color", linky_gen.branding.get_accent_color() != topmedia_gen.branding.get_accent_color()),
        ("Brand Text", linky_gen.branding.get_brand_text() != topmedia_gen.branding.get_brand_text()),
    ]
    
    print("\n✅ Verification Results:")
    all_different = True
    for check_name, is_different in different_checks:
        status = "✅ DIFFERENT" if is_different else "❌ SAME"
        print(f"   {check_name}: {status}")
        if not is_different:
            all_different = False
    
    return all_different


def generate_final_test_videos():
    """Generate final test videos with clear branding differences"""
    print("\n🎬 Generating Final Test Videos...\n")
    
    test_text = "تست نهایی برندینگ: این ویدیو برای تأیید تفاوت برندینگ کانال‌ها تولید شده است"
    
    try:
        # Generate Linky video
        print("📹 Generating Linky video...")
        linky_gen = TextVideoGenerator("linky")
        linky_output = Config.OUTPUT_DIR / "FINAL_TEST_linky.mp4"
        linky_success = linky_gen.generate_video(test_text, linky_output)
        
        if linky_success:
            print(f"✅ Linky video: {linky_output.name}")
            print(f"   Should show: {linky_gen.branding.get_brand_text()}")
            print(f"   Background: {linky_gen.branding.get_background_color()}")
        
        # Generate Top Media video
        print("\n📹 Generating Top Media video...")
        topmedia_gen = TextVideoGenerator("topmedia")
        topmedia_output = Config.OUTPUT_DIR / "FINAL_TEST_topmedia.mp4"
        topmedia_success = topmedia_gen.generate_video(test_text, topmedia_output)
        
        if topmedia_success:
            print(f"✅ Top Media video: {topmedia_output.name}")
            print(f"   Should show: {topmedia_gen.branding.get_brand_text()}")
            print(f"   Background: {topmedia_gen.branding.get_background_color()}")
        
        return linky_success and topmedia_success
        
    except Exception as e:
        print(f"❌ Error generating videos: {e}")
        return False


def verify_config_values():
    """Verify configuration values are correct"""
    print("\n⚙️ Verifying Configuration Values...\n")
    
    print("📋 Linky Configuration:")
    print(f"   CHANNEL_NAME: '{Config.CHANNEL_NAME}'")
    print(f"   INSTAGRAM_HANDLE: '{Config.INSTAGRAM_HANDLE}'")
    
    print("\n📋 Top Media Configuration:")
    print(f"   TOPMEDIA_CHANNEL_NAME: '{Config.TOPMEDIA_CHANNEL_NAME}'")
    print(f"   TOPMEDIA_INSTAGRAM_HANDLE: '{Config.TOPMEDIA_INSTAGRAM_HANDLE}'")
    
    # Check if they're different
    name_different = Config.CHANNEL_NAME != Config.TOPMEDIA_CHANNEL_NAME
    handle_different = Config.INSTAGRAM_HANDLE != Config.TOPMEDIA_INSTAGRAM_HANDLE
    
    print(f"\n✅ Channel names different: {name_different}")
    print(f"✅ Instagram handles different: {handle_different}")
    
    return name_different and handle_different


def main():
    """Run final verification"""
    print("🚀 FINAL BRANDING VERIFICATION\n")
    print("=" * 60)
    
    Config.create_directories()
    
    tests = [
        ("Configuration Values", verify_config_values),
        ("Generator Branding", test_branding_in_generators),
        ("Video Generation", generate_final_test_videos),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            print(f"\n❌ ERROR in {test_name}: {e}")
            results.append((test_name, False))
    
    # Final summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print("\n" + "=" * 60)
    print(f"📊 FINAL RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 SUCCESS! Top Media branding is working correctly!")
        print("\n📁 Check these videos to see the branding differences:")
        print("   • FINAL_TEST_linky.mp4 - Should show @linkychannell with colorful theme")
        print("   • FINAL_TEST_topmedia.mp4 - Should show @topmedi with black/white theme")
        print("\n🔧 Next Steps:")
        print("   1. Set up Top Media YouTube credentials (topmedia_client_secret.json)")
        print("   2. Test posting to both YouTube channels")
        print("   3. Verify videos are posted with correct branding")
        return True
    else:
        print("\n❌ FAILED! Some branding issues still exist.")
        print("Please check the failed tests above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
