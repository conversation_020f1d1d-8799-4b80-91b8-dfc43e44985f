#!/usr/bin/env python3
"""
Test script to verify early morning scheduling works correctly
"""
import sys
from pathlib import Path
from datetime import datetime, time, date
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_early_morning_logic():
    """Test the scheduling logic for early morning times"""
    print("🌅 Testing Early Morning Scheduling Logic")
    print("=" * 60)
    
    try:
        from src.scheduler import PostScheduler
        
        scheduler = PostScheduler()
        tehran_tz = scheduler.tehran_tz
        
        # Simulate the conditions from the logs (1:03 AM)
        print("Simulating conditions from logs (1:03 AM Tehran time):")
        
        # Get today's date
        today = date.today()
        
        # Test the logic manually
        current_hour = 1  # 1:03 AM
        posting_start_hour = 9
        posting_end_hour = 24
        
        print(f"Current hour: {current_hour}")
        print(f"Posting window: {posting_start_hour}-{posting_end_hour}")
        print(f"In posting window: {posting_start_hour <= current_hour < posting_end_hour}")
        
        # Test the optimal posting times
        optimal_times = scheduler._get_optimal_posting_times()
        print(f"Optimal posting times: {optimal_times}")
        
        # Simulate the logic from _get_next_available_slot
        today_slots = []  # Assume no slots taken yet
        
        # Test the new logic
        available_hours = []
        for h in optimal_times:
            if h not in today_slots:
                # If we're currently in posting window, only allow current hour and future hours
                if posting_start_hour <= current_hour < posting_end_hour:
                    if h >= current_hour:
                        available_hours.append(h)
                # If we're outside posting window (early morning), allow all posting hours for today
                else:
                    available_hours.append(h)
        
        print(f"Available hours: {available_hours}")
        
        if available_hours:
            target_hour = min(available_hours)
            print(f"Target hour: {target_hour}")
            
            # Should be 9 AM (first available slot)
            if target_hour == 9:
                print("✅ Correctly selects 9 AM for early morning scheduling")
            else:
                print(f"❌ Should select 9 AM, but selected {target_hour}")
                
            # Create target time for today
            target_time = tehran_tz.localize(
                datetime.combine(today, time(hour=target_hour, minute=30))
            )
            
            print(f"Target time: {target_time}")
            
            if target_time.date() == today:
                print("✅ Correctly schedules for TODAY")
            else:
                print("❌ Incorrectly schedules for tomorrow")
        else:
            print("❌ No available hours found")
        
        return True
        
    except Exception as e:
        print(f"❌ Early morning test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_current_scheduling():
    """Test current scheduling behavior"""
    print("\n🕐 Testing Current Scheduling Behavior")
    print("=" * 60)
    
    try:
        from src.scheduler import PostScheduler
        
        scheduler = PostScheduler()
        
        # Clear existing scheduled posts to test fresh
        original_posts = scheduler.scheduled_posts.copy()
        scheduler.scheduled_posts = []
        
        # Test scheduling
        next_slot = scheduler._get_next_available_slot()
        next_slot_tehran = pytz.UTC.localize(next_slot).astimezone(scheduler.tehran_tz)
        
        now_tehran = datetime.now(scheduler.tehran_tz)
        
        print(f"Current time: {now_tehran.strftime('%Y-%m-%d %H:%M %Z')}")
        print(f"Next slot: {next_slot_tehran.strftime('%Y-%m-%d %H:%M %Z')}")
        
        # Check if scheduled for today
        if next_slot_tehran.date() == now_tehran.date():
            print("✅ Scheduled for TODAY")
        else:
            print("❌ Scheduled for TOMORROW")
            
        # Restore original posts
        scheduler.scheduled_posts = original_posts
        
        return True
        
    except Exception as e:
        print(f"❌ Current scheduling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Early Morning Scheduling Fix")
    print("=" * 80)
    
    tests = [
        test_early_morning_logic,
        test_current_scheduling,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Early morning scheduling fix is working correctly!")
        return True
    else:
        print("⚠️  Early morning scheduling issues may still exist")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ Early morning scheduling verified successfully!")
        else:
            print("\n❌ Early morning scheduling issues detected!")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback
        traceback.print_exc()
