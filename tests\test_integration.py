"""
Integration tests for the complete workflow
"""
import pytest
import asyncio
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch, AsyncMock

from src.main_app import LinkInstaApp
from src.config import Config

class TestIntegration:
    """Integration test cases"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_config(self, temp_dir):
        """Mock configuration for integration tests"""
        with patch.object(Config, 'BASE_DIR', temp_dir):
            with patch.object(Config, 'TEMP_DIR', temp_dir / 'temp'):
                with patch.object(Config, 'AUDIO_DIR', temp_dir / 'audio'):
                    with patch.object(Config, 'OUTPUT_DIR', temp_dir / 'output'):
                        with patch.object(Config, 'LOGS_DIR', temp_dir / 'logs'):
                            # Create directories
                            for dir_path in [Config.TEMP_DIR, Config.AUDIO_DIR, Config.OUTPUT_DIR, Config.LOGS_DIR]:
                                dir_path.mkdir(exist_ok=True)
                            yield Config
    
    @pytest.fixture
    def mock_app(self, mock_config):
        """Create mocked LinkInstaApp for testing"""
        with patch.object(Config, 'TELEGRAM_BOT_TOKEN', 'fake_token'):
            with patch.object(Config, 'INSTAGRAM_USERNAME', 'fake_user'):
                with patch.object(Config, 'INSTAGRAM_PASSWORD', 'fake_pass'):
                    app = LinkInstaApp()
                    return app
    
    @pytest.mark.asyncio
    async def test_app_initialization(self, mock_app):
        """Test application initialization"""
        # Mock external dependencies
        mock_app.audio_manager.download_copyright_free_music = Mock(return_value=True)
        mock_app.instagram_client.login = Mock(return_value=True)
        
        # Create some fake audio files to skip download
        audio_files = []
        for i in range(6):
            audio_file = Config.AUDIO_DIR / f'track_{i}.mp3'
            audio_file.write_text("fake audio")
            audio_files.append(audio_file)
        
        result = await mock_app.initialize()
        
        assert result is True
        mock_app.instagram_client.login.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_message_processing_workflow(self, mock_app):
        """Test complete message processing workflow"""
        # Mock telegram message
        mock_message_data = {
            'message_id': 12345,
            'text': 'Test message for processing',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        
        # Mock message processor
        mock_app.message_processor.poll_and_process_messages = AsyncMock()
        
        # Create mock processed message
        from src.telegram_client import TelegramMessage
        from src.message_processor import ProcessedMessage
        
        telegram_msg = TelegramMessage(mock_message_data)
        processed_msg = ProcessedMessage(telegram_msg)
        
        mock_app.message_processor.poll_and_process_messages.return_value = [processed_msg]
        
        # Mock video generation
        mock_app.text_video_generator.generate_video = Mock(return_value=True)
        mock_app.message_processor.mark_video_generated = Mock()
        
        # Test processing
        result = await mock_app.process_new_messages()
        
        assert result == 1  # One message processed
        mock_app.text_video_generator.generate_video.assert_called_once()
        mock_app.message_processor.mark_video_generated.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_video_generation_for_different_types(self, mock_app):
        """Test video generation for different message types"""
        from src.telegram_client import TelegramMessage
        from src.message_processor import ProcessedMessage
        
        # Test text-only message
        text_data = {
            'message_id': 1,
            'text': 'Text only message',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        text_msg = ProcessedMessage(TelegramMessage(text_data))
        
        mock_app.text_video_generator.generate_video = Mock(return_value=True)
        mock_app.message_processor.mark_video_generated = Mock()
        
        result = await mock_app._generate_video_for_message(text_msg)
        assert result is True
        mock_app.text_video_generator.generate_video.assert_called_once()
        
        # Test text+image message
        image_data = {
            'message_id': 2,
            'text': 'Text with image',
            'date': None,
            'media_type': 'photo',
            'media_files': [{'type': 'photo'}],
            'caption': ''
        }
        image_msg = ProcessedMessage(TelegramMessage(image_data))
        image_msg.media_paths = [Path('fake_image.jpg')]
        
        mock_app.text_image_generator.generate_video = Mock(return_value=True)
        
        result = await mock_app._generate_video_for_message(image_msg)
        assert result is True
        mock_app.text_image_generator.generate_video.assert_called_once()
        
        # Test video+text message
        video_data = {
            'message_id': 3,
            'text': 'Video with text',
            'date': None,
            'media_type': 'video',
            'media_files': [{'type': 'video'}],
            'caption': ''
        }
        video_msg = ProcessedMessage(TelegramMessage(video_data))
        video_msg.media_paths = [Path('fake_video.mp4')]
        
        mock_app.video_text_generator.generate_video = Mock(return_value=True)
        
        result = await mock_app._generate_video_for_message(video_msg)
        assert result is True
        mock_app.video_text_generator.generate_video.assert_called_once()
    
    def test_scheduling_workflow(self, mock_app):
        """Test post scheduling workflow"""
        from src.telegram_client import TelegramMessage
        from src.message_processor import ProcessedMessage
        
        # Create mock processed message with generated video
        message_data = {
            'message_id': 123,
            'text': 'Scheduled message',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        
        processed_msg = ProcessedMessage(TelegramMessage(message_data))
        processed_msg.video_generated = True
        processed_msg.output_video_path = Path('fake_video.mp4')
        
        # Mock message processor methods
        mock_app.message_processor.get_ready_for_posting = Mock(return_value=[processed_msg])
        mock_app.scheduler.schedule_post = Mock(return_value=True)
        
        # Test scheduling
        result = mock_app.schedule_ready_posts()
        
        assert result == 1  # One post scheduled
        mock_app.scheduler.schedule_post.assert_called_once_with(processed_msg)
    
    @pytest.mark.asyncio
    async def test_complete_cycle(self, mock_app):
        """Test complete processing cycle"""
        # Mock all components
        mock_app.process_new_messages = AsyncMock(return_value=2)
        mock_app.schedule_ready_posts = Mock(return_value=1)
        mock_app.message_processor.cleanup_old_temp_files = Mock()
        mock_app.scheduler.cleanup_old_entries = Mock(return_value=0)
        
        # Run cycle
        await mock_app.run_cycle()
        
        # Verify all components were called
        mock_app.process_new_messages.assert_called_once()
        mock_app.schedule_ready_posts.assert_called_once()
        mock_app.message_processor.cleanup_old_temp_files.assert_called_once()
        mock_app.scheduler.cleanup_old_entries.assert_called_once()
    
    def test_app_status_reporting(self, mock_app):
        """Test application status reporting"""
        # Mock component statistics
        mock_app.message_processor.get_statistics = Mock(return_value={
            'total_messages': 10,
            'pending_processing': 2
        })
        mock_app.audio_manager.get_usage_statistics = Mock(return_value={
            'total_tracks': 20
        })
        mock_app.instagram_client.get_post_statistics = Mock(return_value={
            'total_successful_posts': 5
        })
        mock_app.scheduler.get_schedule_status = Mock(return_value={
            'total_scheduled': 3
        })
        
        status = mock_app.get_status()
        
        assert 'is_running' in status
        assert 'message_processing' in status
        assert 'audio_management' in status
        assert 'instagram_posting' in status
        assert 'scheduling' in status
        
        # Verify component methods were called
        mock_app.message_processor.get_statistics.assert_called_once()
        mock_app.audio_manager.get_usage_statistics.assert_called_once()
        mock_app.instagram_client.get_post_statistics.assert_called_once()
        mock_app.scheduler.get_schedule_status.assert_called_once()
    
    def test_error_handling_in_cycle(self, mock_app):
        """Test error handling during processing cycle"""
        # Mock a component to raise an exception
        mock_app.process_new_messages = AsyncMock(side_effect=Exception("Test error"))
        mock_app.schedule_ready_posts = Mock(return_value=0)
        mock_app.message_processor.cleanup_old_temp_files = Mock()
        mock_app.scheduler.cleanup_old_entries = Mock(return_value=0)
        
        # Run cycle - should not raise exception
        async def test_cycle():
            await mock_app.run_cycle()
        
        # Should complete without raising exception
        asyncio.run(test_cycle())
    
    def test_graceful_shutdown(self, mock_app):
        """Test graceful application shutdown"""
        # Mock scheduler and instagram client
        mock_app.scheduler.stop_scheduler = Mock()
        mock_app.instagram_client.logout = Mock()
        
        # Test stop
        mock_app.stop()
        
        assert mock_app.is_running is False
        mock_app.scheduler.stop_scheduler.assert_called_once()
        mock_app.instagram_client.logout.assert_called_once()

if __name__ == '__main__':
    pytest.main([__file__])
