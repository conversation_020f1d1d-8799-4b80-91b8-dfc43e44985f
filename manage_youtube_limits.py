#!/usr/bin/env python3
"""
YouTube Upload Limit Management Script

This script helps manage YouTube upload limits by:
1. Temporarily disabling YouTube posting when limits are exceeded
2. Re-enabling YouTube posting after the limit reset period
3. Checking current YouTube quota status
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
import json

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from config import Config
from utils.logger import logger


def read_env_file():
    """Read the .env file and return its contents as a dict"""
    env_file = Path(".env")
    if not env_file.exists():
        logger.error(".env file not found")
        return None
    
    env_vars = {}
    with open(env_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
    
    return env_vars


def write_env_file(env_vars):
    """Write the env_vars dict back to the .env file"""
    env_file = Path(".env")
    with open(env_file, 'w', encoding='utf-8') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")


def disable_youtube_posting():
    """Temporarily disable YouTube posting due to upload limits"""
    env_vars = read_env_file()
    if env_vars is None:
        return False
    
    env_vars['ENABLE_YOUTUBE_POSTING'] = 'false'
    write_env_file(env_vars)
    
    # Save the disable timestamp
    limit_info = {
        'disabled_at': datetime.now().isoformat(),
        'reason': 'upload_limit_exceeded',
        'auto_enable_at': (datetime.now() + timedelta(hours=24)).isoformat()
    }
    
    with open('youtube_limit_status.json', 'w') as f:
        json.dump(limit_info, f, indent=2)
    
    logger.info("🚫 YouTube posting disabled due to upload limits")
    logger.info("📅 Will auto-enable after 24 hours")
    return True


def enable_youtube_posting():
    """Re-enable YouTube posting"""
    env_vars = read_env_file()
    if env_vars is None:
        return False
    
    env_vars['ENABLE_YOUTUBE_POSTING'] = 'true'
    write_env_file(env_vars)
    
    # Remove the limit status file
    limit_file = Path('youtube_limit_status.json')
    if limit_file.exists():
        limit_file.unlink()
    
    logger.info("✅ YouTube posting re-enabled")
    return True


def check_youtube_status():
    """Check current YouTube posting status and limits"""
    env_vars = read_env_file()
    if env_vars is None:
        return
    
    youtube_enabled = env_vars.get('ENABLE_YOUTUBE_POSTING', 'false').lower() == 'true'
    
    print(f"📊 YouTube Posting Status: {'✅ ENABLED' if youtube_enabled else '🚫 DISABLED'}")
    
    # Check if there's a limit status file
    limit_file = Path('youtube_limit_status.json')
    if limit_file.exists():
        try:
            with open(limit_file, 'r') as f:
                limit_info = json.load(f)
            
            disabled_at = datetime.fromisoformat(limit_info['disabled_at'])
            auto_enable_at = datetime.fromisoformat(limit_info['auto_enable_at'])
            now = datetime.now()
            
            print(f"🕒 Disabled at: {disabled_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"🔄 Auto-enable at: {auto_enable_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if now >= auto_enable_at:
                print("⏰ Auto-enable time has passed. You can re-enable YouTube posting now.")
            else:
                time_left = auto_enable_at - now
                hours_left = int(time_left.total_seconds() / 3600)
                minutes_left = int((time_left.total_seconds() % 3600) / 60)
                print(f"⏳ Time until auto-enable: {hours_left}h {minutes_left}m")
        
        except Exception as e:
            logger.error(f"Error reading limit status: {e}")


def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("YouTube Upload Limit Manager")
        print("Usage:")
        print("  python manage_youtube_limits.py status    - Check current status")
        print("  python manage_youtube_limits.py disable   - Disable YouTube posting")
        print("  python manage_youtube_limits.py enable    - Enable YouTube posting")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'status':
        check_youtube_status()
    elif command == 'disable':
        disable_youtube_posting()
    elif command == 'enable':
        enable_youtube_posting()
    else:
        print(f"Unknown command: {command}")
        print("Available commands: status, disable, enable")


if __name__ == "__main__":
    main()
