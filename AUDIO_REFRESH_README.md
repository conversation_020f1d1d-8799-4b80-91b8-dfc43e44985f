# Audio Refresh Automation

This standalone script automatically refreshes your audio files with trending music from Pixabay every day at 1:00 AM Tehran time.

## Features

- 🕐 **Scheduled Execution**: Runs daily at 1:00 AM Tehran time
- 🎵 **Trending Music**: Fetches trending music from Pixabay
- 🔄 **Auto Cleanup**: Removes old audio files before downloading new ones
- 📊 **Usage Tracking**: Maintains usage statistics for audio files
- 🐍 **Isolated Environment**: Uses its own virtual environment in `/audio/venv`
- 📝 **Logging**: Comprehensive logging to `audio_refresh.log`

## Quick Setup

1. **Run the setup script**:
   ```bash
   python setup_audio_refresh.py
   ```

2. **Test immediately** (optional):
   - Windows: `test_audio_refresh.bat`
   - Linux/Mac: `./test_audio_refresh.sh`

That's it! The script will now run automatically every day at 1:00 AM Tehran time.

## Files Created

- `audio/venv/` - Virtual environment for the script
- `run_audio_refresh.bat/.sh` - Script to run the audio refresh
- `test_audio_refresh.bat/.sh` - <PERSON>ript to test immediately
- `audio_refresh.log` - Log file with execution history

## Configuration

### Pixabay API Key (Optional but Recommended)

For better music variety, set your Pixabay API key:

1. Get a free API key from [Pixabay](https://pixabay.com/api/docs/)
2. Set environment variable:
   - Windows: `set PIXABAY_API_KEY=your_key_here`
   - Linux/Mac: `export PIXABAY_API_KEY=your_key_here`

### Audio Directory

The script uses the `audio/` directory by default. All old audio files will be removed and replaced with new trending music.

## How It Works

1. **Daily Schedule**: The script runs at 1:00 AM Tehran time
2. **Cleanup**: Removes all existing audio files (except usage tracking)
3. **Download**: Fetches 20 trending music tracks from Pixabay
4. **Tracking**: Updates usage tracking for the new files

## Manual Execution

To run the refresh immediately:

```bash
# Windows
test_audio_refresh.bat

# Linux/Mac
./test_audio_refresh.sh

# Or directly with Python
cd audio
venv/Scripts/python ../audio_refresh_script.py --run-now  # Windows
venv/bin/python ../audio_refresh_script.py --run-now     # Linux/Mac
```

## Scheduler Setup

### Windows
The setup script automatically creates a Windows Task Scheduler task named "AudioRefreshDaily".

To manage manually:
- Open Task Scheduler
- Look for "AudioRefreshDaily" task
- You can disable, modify, or delete it as needed

### Linux/Mac
The setup script provides cron job instructions. To set up manually:

1. Open crontab: `crontab -e`
2. Add this line: `0 1 * * * /path/to/run_audio_refresh.sh`
3. Save and exit

## Troubleshooting

### Check Logs
```bash
tail -f audio_refresh.log
```

### Test Connection
```bash
# Test if script can run
python audio_refresh_script.py --run-now
```

### Verify Schedule
- **Windows**: Check Task Scheduler for "AudioRefreshDaily"
- **Linux/Mac**: Run `crontab -l` to see scheduled jobs

### Common Issues

1. **Permission Denied**: Run setup as Administrator (Windows) or with sudo (Linux/Mac)
2. **Network Issues**: Check internet connection and firewall settings
3. **API Limits**: If using Pixabay API, check your daily limits

## Customization

### Change Schedule Time
Edit the scheduler setup in `setup_audio_refresh.py` or modify the scheduled task/cron job directly.

### Change Number of Tracks
Edit `audio_refresh_script.py` and modify the `download_new_audio(20)` call.

### Add More Music Sources
Extend the `PixabayAudioDownloader` class to support additional music sources.

## Integration with Main App

The refreshed audio files will be automatically used by your main LinkInsta application. The existing `AudioManager` class will pick up the new files for video generation.

## Security Notes

- The script only downloads from trusted sources (Pixabay)
- All downloads are scanned for proper audio formats
- No sensitive data is stored or transmitted
- Virtual environment isolates dependencies

## Support

If you encounter issues:

1. Check the log file: `audio_refresh.log`
2. Test manual execution: `python audio_refresh_script.py --run-now`
3. Verify your internet connection and Pixabay access
4. Ensure proper permissions for file operations

---

**Note**: This script is designed to work independently of your main application and uses its own virtual environment to avoid conflicts.
