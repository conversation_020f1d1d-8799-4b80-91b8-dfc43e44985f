#!/usr/bin/env python3
"""
Test script to verify the robust file cleanup fix
"""
import os
import sys
import time
import tempfile
import threading
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from utils.file_utils import (
    safe_delete_file, 
    cleanup_video_file_robust, 
    cleanup_video_file_threaded,
    cleanup_multiple_files,
    is_file_in_use,
    get_processes_using_file,
    force_garbage_collection
)


def create_test_video_file(file_path: Path) -> bool:
    """Create a test video file for testing"""
    try:
        # Create a simple test file (not a real video, but sufficient for testing)
        with open(file_path, 'wb') as f:
            f.write(b'FAKE_VIDEO_DATA' * 1000)  # Create some content
        return True
    except Exception as e:
        print(f"Error creating test file: {e}")
        return False


def test_basic_cleanup():
    """Test basic file cleanup functionality"""
    print("\n=== Testing Basic Cleanup ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = Path(temp_dir) / "test_video.mp4"
        
        # Create test file
        if not create_test_video_file(test_file):
            print("❌ Failed to create test file")
            return False
        
        print(f"✅ Created test file: {test_file.name}")
        
        # Test safe deletion
        success = safe_delete_file(test_file)
        
        if success and not test_file.exists():
            print("✅ Basic cleanup test passed")
            return True
        else:
            print("❌ Basic cleanup test failed")
            return False


def test_file_in_use_detection():
    """Test file-in-use detection"""
    print("\n=== Testing File-in-Use Detection ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = Path(temp_dir) / "test_video.mp4"
        
        # Create test file
        if not create_test_video_file(test_file):
            print("❌ Failed to create test file")
            return False
        
        # Open file to simulate it being in use
        with open(test_file, 'rb') as f:
            # Check if file is detected as in use
            in_use = is_file_in_use(test_file)
            print(f"File in use detection: {in_use}")
            
            # Try to get processes using the file
            processes = get_processes_using_file(test_file)
            print(f"Processes using file: {processes}")
            
        print("✅ File-in-use detection test completed")
        return True


def test_robust_cleanup():
    """Test robust cleanup with simulated file lock"""
    print("\n=== Testing Robust Cleanup ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = Path(temp_dir) / "test_video.mp4"
        
        # Create test file
        if not create_test_video_file(test_file):
            print("❌ Failed to create test file")
            return False
        
        print(f"✅ Created test file: {test_file.name}")
        
        # Test robust cleanup
        success = cleanup_video_file_robust(test_file, delay_before_cleanup=1.0)
        
        if success and not test_file.exists():
            print("✅ Robust cleanup test passed")
            return True
        else:
            print("❌ Robust cleanup test failed")
            return False


def test_threaded_cleanup():
    """Test threaded cleanup functionality"""
    print("\n=== Testing Threaded Cleanup ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = Path(temp_dir) / "test_video.mp4"
        
        # Create test file
        if not create_test_video_file(test_file):
            print("❌ Failed to create test file")
            return False
        
        print(f"✅ Created test file: {test_file.name}")
        
        # Start threaded cleanup
        cleanup_video_file_threaded(test_file, delay_before_cleanup=1.0)
        
        # Wait for cleanup to complete
        time.sleep(3.0)
        
        if not test_file.exists():
            print("✅ Threaded cleanup test passed")
            return True
        else:
            print("❌ Threaded cleanup test failed")
            return False


def test_multiple_files_cleanup():
    """Test cleanup of multiple files"""
    print("\n=== Testing Multiple Files Cleanup ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_files = []
        
        # Create multiple test files
        for i in range(3):
            test_file = Path(temp_dir) / f"test_video_{i}.mp4"
            if create_test_video_file(test_file):
                test_files.append(test_file)
        
        print(f"✅ Created {len(test_files)} test files")
        
        # Test multiple file cleanup
        cleaned_count = cleanup_multiple_files(test_files, delay_between_files=0.1)
        
        remaining_files = [f for f in test_files if f.exists()]
        
        if cleaned_count == len(test_files) and len(remaining_files) == 0:
            print(f"✅ Multiple files cleanup test passed ({cleaned_count} files cleaned)")
            return True
        else:
            print(f"❌ Multiple files cleanup test failed ({cleaned_count}/{len(test_files)} cleaned, {len(remaining_files)} remaining)")
            return False


def test_garbage_collection():
    """Test garbage collection functionality"""
    print("\n=== Testing Garbage Collection ===")
    
    try:
        force_garbage_collection()
        print("✅ Garbage collection test passed")
        return True
    except Exception as e:
        print(f"❌ Garbage collection test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🧪 Starting File Cleanup Fix Tests")
    print("=" * 50)
    
    tests = [
        test_basic_cleanup,
        test_file_in_use_detection,
        test_robust_cleanup,
        test_threaded_cleanup,
        test_multiple_files_cleanup,
        test_garbage_collection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The file cleanup fix is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
