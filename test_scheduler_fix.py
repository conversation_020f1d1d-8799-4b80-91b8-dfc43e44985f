#!/usr/bin/env python3
"""
Test script to verify the Instagram scheduler fix
"""
import os
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from config import Config
from message_processor import MessageProcessor
from scheduler import PostScheduler


def test_video_status_sync():
    """Test the video status sync functionality"""
    print("\n=== Testing Video Status Sync ===")
    
    try:
        processor = MessageProcessor()
        
        # Check current status
        total_messages = len(processor.processing_queue)
        videos_generated = len([msg for msg in processor.processing_queue if msg.video_generated])
        
        print(f"📊 Total messages in queue: {total_messages}")
        print(f"📊 Videos marked as generated: {videos_generated}")
        
        # Sync status with actual files
        processor.sync_video_status_with_files()
        
        # Check status after sync
        videos_generated_after = len([msg for msg in processor.processing_queue if msg.video_generated])
        
        print(f"📊 Videos marked as generated after sync: {videos_generated_after}")
        
        if videos_generated_after > videos_generated:
            print(f"✅ Sync fixed {videos_generated_after - videos_generated} video statuses")
        else:
            print("✅ Video statuses were already in sync")
            
        return True
        
    except Exception as e:
        print(f"❌ Error in video status sync test: {e}")
        return False


def test_ready_for_posting():
    """Test getting messages ready for posting"""
    print("\n=== Testing Ready for Posting ===")
    
    try:
        processor = MessageProcessor()
        ready_messages = processor.get_ready_for_posting()
        
        print(f"📊 Messages ready for posting: {len(ready_messages)}")
        
        for i, msg in enumerate(ready_messages[:5], 1):  # Show first 5
            print(f"  {i}. Message {msg.telegram_message.message_id} - {msg.content_type}")
        
        if len(ready_messages) > 5:
            print(f"  ... and {len(ready_messages) - 5} more")
        
        return len(ready_messages) > 0
        
    except Exception as e:
        print(f"❌ Error in ready for posting test: {e}")
        return False


def test_hourly_posting_logic():
    """Test the hourly posting logic"""
    print("\n=== Testing Hourly Posting Logic ===")
    
    try:
        scheduler = PostScheduler()
        
        # Test the hourly posting logic
        scheduled_count = scheduler.ensure_hourly_posting()
        
        print(f"📊 Posts scheduled by hourly logic: {scheduled_count}")
        
        if scheduled_count > 0:
            print("✅ Hourly posting logic successfully scheduled a post")
        else:
            print("ℹ️  No posts scheduled (may be outside posting hours or no videos available)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in hourly posting test: {e}")
        return False


def test_config_settings():
    """Test configuration settings"""
    print("\n=== Testing Configuration Settings ===")
    
    try:
        print(f"📊 Poll interval: {Config.POLL_INTERVAL_MINUTES} minutes")
        print(f"📊 Test mode: {Config.TEST_MODE}")
        print(f"📊 Posting hours: {Config.POSTING_HOURS_START}-{Config.POSTING_HOURS_END}")
        print(f"📊 Instagram posting enabled: {Config.ENABLE_INSTAGRAM_POSTING}")
        
        if Config.TEST_MODE:
            print("✅ Test mode is enabled - Instagram posting will be simulated")
        else:
            print("⚠️  Test mode is disabled - Instagram posting will be real")
        
        if Config.POLL_INTERVAL_MINUTES <= 5:
            print("✅ Poll interval is set for fast testing")
        else:
            print("ℹ️  Poll interval is set for production")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in config test: {e}")
        return False


def test_schedule_status():
    """Test schedule status"""
    print("\n=== Testing Schedule Status ===")
    
    try:
        scheduler = PostScheduler()
        status = scheduler.get_schedule_status()
        
        print(f"📊 Total scheduled posts: {status.get('total_posts', 0)}")
        print(f"📊 Pending posts: {status.get('pending_posts', 0)}")
        print(f"📊 Completed posts: {status.get('completed_posts', 0)}")
        print(f"📊 Failed posts: {status.get('failed_posts', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in schedule status test: {e}")
        return False


def show_current_time_info():
    """Show current time information"""
    print("\n=== Current Time Information ===")
    
    try:
        from datetime import datetime
        import pytz
        
        tehran_tz = pytz.timezone('Asia/Tehran')
        current_time = datetime.now(tehran_tz)
        current_hour = current_time.hour
        
        print(f"🕐 Current time (Tehran): {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🕐 Current hour: {current_hour}")
        
        posting_start = Config.POSTING_HOURS_START
        posting_end = Config.POSTING_HOURS_END
        
        if posting_start <= current_hour < posting_end:
            print(f"✅ Currently in posting hours ({posting_start}-{posting_end})")
        else:
            print(f"⏰ Outside posting hours ({posting_start}-{posting_end})")
            
        return True
        
    except Exception as e:
        print(f"❌ Error getting time info: {e}")
        return False


def main():
    """Run all tests"""
    print("🧪 Starting Instagram Scheduler Fix Tests")
    print("=" * 60)
    
    tests = [
        show_current_time_info,
        test_config_settings,
        test_video_status_sync,
        test_ready_for_posting,
        test_schedule_status,
        test_hourly_posting_logic,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scheduler fix is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Run the main application with: python main.py")
        print("2. Monitor logs for proper video status sync")
        print("3. Verify posts are scheduled correctly in test mode")
        print("4. Check that videos are being posted at expected intervals")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
