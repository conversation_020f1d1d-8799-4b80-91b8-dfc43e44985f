#!/usr/bin/env python3
"""
Test script to verify branding fixes
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from utils.branding import ChannelBranding
from video_generators.text_video_generator import TextVideoGenerator
from youtube_client import YouTubeClient


def test_config_values():
    """Test configuration values are properly loaded"""
    print("🔧 Testing Configuration Values...")
    
    print(f"Linky YouTube Title: '{Config.YOUTUBE_DEFAULT_TITLE}'")
    print(f"Linky YouTube Description: '{Config.YOUTUBE_DEFAULT_DESCRIPTION}'")
    print(f"Top Media YouTube Title: '{Config.TOPMEDIA_YOUTUBE_DEFAULT_TITLE}'")
    print(f"Top Media YouTube Description: '{Config.TOPMEDIA_YOUTUBE_DEFAULT_DESCRIPTION}'")
    
    # Check if values are different
    title_different = Config.YOUTUBE_DEFAULT_TITLE != Config.TOPMEDIA_YOUTUBE_DEFAULT_TITLE
    desc_different = Config.YOUTUBE_DEFAULT_DESCRIPTION != Config.TOPMEDIA_YOUTUBE_DEFAULT_DESCRIPTION
    
    print(f"✅ Titles are different: {title_different}")
    print(f"✅ Descriptions are different: {desc_different}")
    
    return title_different and desc_different


def test_branding_objects():
    """Test branding objects return correct values"""
    print("\n🎨 Testing Branding Objects...")
    
    linky_branding = ChannelBranding("linky")
    topmedia_branding = ChannelBranding("topmedia")
    
    print(f"Linky brand text: '{linky_branding.get_brand_text()}'")
    print(f"Top Media brand text: '{topmedia_branding.get_brand_text()}'")
    
    print(f"Linky channel name: '{linky_branding.get_channel_name()}'")
    print(f"Top Media channel name: '{topmedia_branding.get_channel_name()}'")
    
    print(f"Linky background: {linky_branding.get_background_color()}")
    print(f"Top Media background: {topmedia_branding.get_background_color()}")
    
    # Check if values are different
    brand_text_different = linky_branding.get_brand_text() != topmedia_branding.get_brand_text()
    channel_name_different = linky_branding.get_channel_name() != topmedia_branding.get_channel_name()
    background_different = linky_branding.get_background_color() != topmedia_branding.get_background_color()
    
    print(f"✅ Brand texts are different: {brand_text_different}")
    print(f"✅ Channel names are different: {channel_name_different}")
    print(f"✅ Backgrounds are different: {background_different}")
    
    return brand_text_different and channel_name_different and background_different


def test_youtube_clients():
    """Test YouTube clients have different configurations"""
    print("\n📺 Testing YouTube Clients...")
    
    linky_client = YouTubeClient("linky")
    topmedia_client = YouTubeClient("topmedia")
    
    print(f"Linky client channel name: '{linky_client.channel_name}'")
    print(f"Top Media client channel name: '{topmedia_client.channel_name}'")
    
    print(f"Linky default title: '{linky_client.default_title}'")
    print(f"Top Media default title: '{topmedia_client.default_title}'")
    
    print(f"Linky default description: '{linky_client.default_description}'")
    print(f"Top Media default description: '{topmedia_client.default_description}'")
    
    print(f"Linky credentials file: {linky_client.credentials_file}")
    print(f"Top Media credentials file: {topmedia_client.credentials_file}")
    
    # Check if values are different
    name_different = linky_client.channel_name != topmedia_client.channel_name
    title_different = linky_client.default_title != topmedia_client.default_title
    desc_different = linky_client.default_description != topmedia_client.default_description
    creds_different = linky_client.credentials_file != topmedia_client.credentials_file
    
    print(f"✅ Channel names are different: {name_different}")
    print(f"✅ Default titles are different: {title_different}")
    print(f"✅ Default descriptions are different: {desc_different}")
    print(f"✅ Credentials files are different: {creds_different}")
    
    return name_different and title_different and desc_different and creds_different


def generate_test_videos():
    """Generate test videos to verify branding"""
    print("\n📹 Generating Test Videos...")
    
    test_text = "تست برندینگ: این متن برای بررسی تفاوت برندینگ کانال‌ها است"
    
    try:
        # Generate Linky video
        linky_generator = TextVideoGenerator("linky")
        linky_output = Config.OUTPUT_DIR / "branding_test_linky.mp4"
        linky_success = linky_generator.generate_video(test_text, linky_output)
        
        # Generate Top Media video
        topmedia_generator = TextVideoGenerator("topmedia")
        topmedia_output = Config.OUTPUT_DIR / "branding_test_topmedia.mp4"
        topmedia_success = topmedia_generator.generate_video(test_text, topmedia_output)
        
        if linky_success and topmedia_success:
            print(f"✅ Generated Linky video: {linky_output.name}")
            print(f"✅ Generated Top Media video: {topmedia_output.name}")
            print("📁 Check the videos to verify different branding:")
            print("   - Linky: Should have @linkychannell and colorful theme")
            print("   - Top Media: Should have @topmedi and black/white theme")
            return True
        else:
            print("❌ Video generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error generating videos: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Testing Branding Fixes...\n")
    
    Config.create_directories()
    
    tests = [
        ("Configuration Values", test_config_values),
        ("Branding Objects", test_branding_objects),
        ("YouTube Clients", test_youtube_clients),
        ("Video Generation", generate_test_videos),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All branding fixes verified! Top Media should now have proper branding.")
    else:
        print("⚠️ Some tests failed. Branding issues may still exist.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
