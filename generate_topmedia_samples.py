#!/usr/bin/env python3
"""
Generate sample videos for Top Media channel to demonstrate branding differences
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from video_generators.text_video_generator import TextVideoGenerator
from video_generators.text_image_video_generator import TextImageVideoGenerator
from utils.logger import logger


def generate_sample_videos():
    """Generate sample videos for both channels"""
    print("🎬 Generating sample videos for both channels...\n")
    
    # Sample content
    sample_texts = [
        "خبر فوری: تکنولوژی جدید هوش مصنوعی معرفی شد",
        "آخرین اخبار تکنولوژی و نوآوری در دنیا",
        "گزارش ویژه از بازار ارزهای دیجیتال امروز"
    ]
    
    results = []
    
    for i, text in enumerate(sample_texts, 1):
        print(f"📝 Generating video {i}: {text[:50]}...")
        
        # Generate Linky version
        try:
            linky_generator = TextVideoGenerator("linky")
            linky_output = Config.OUTPUT_DIR / f"sample_linky_{i}.mp4"
            linky_success = linky_generator.generate_video(text, linky_output)
            
            if linky_success:
                print(f"✅ Linky version: {linky_output.name}")
            else:
                print(f"❌ Linky version failed")
            
        except Exception as e:
            print(f"❌ Linky error: {e}")
            linky_success = False
        
        # Generate Top Media version
        try:
            topmedia_generator = TextVideoGenerator("topmedia")
            topmedia_output = Config.OUTPUT_DIR / f"sample_topmedia_{i}.mp4"
            topmedia_success = topmedia_generator.generate_video(text, topmedia_output)
            
            if topmedia_success:
                print(f"✅ Top Media version: {topmedia_output.name}")
            else:
                print(f"❌ Top Media version failed")
            
        except Exception as e:
            print(f"❌ Top Media error: {e}")
            topmedia_success = False
        
        results.append((i, linky_success, topmedia_success))
        print()
    
    # Summary
    total_linky = sum(1 for _, linky, _ in results if linky)
    total_topmedia = sum(1 for _, _, topmedia in results if topmedia)
    
    print(f"📊 Generation Summary:")
    print(f"   Linky videos: {total_linky}/{len(sample_texts)}")
    print(f"   Top Media videos: {total_topmedia}/{len(sample_texts)}")
    
    if total_linky > 0 and total_topmedia > 0:
        print("\n🎉 Sample videos generated successfully!")
        print("📁 Check the output_videos folder to see the different branding:")
        print("   - Linky videos: Colorful theme with @linkychannell branding")
        print("   - Top Media videos: Black/white theme with @topmedi branding")
        return True
    else:
        print("\n⚠️ Some videos failed to generate")
        return False


def show_branding_differences():
    """Show the branding differences between channels"""
    print("🎨 Branding Differences:\n")
    
    from utils.branding import ChannelBranding
    
    linky_branding = ChannelBranding("linky")
    topmedia_branding = ChannelBranding("topmedia")
    
    print("📺 LINKY CHANNEL:")
    print(f"   Channel Name: {linky_branding.get_channel_name()}")
    print(f"   Instagram Handle: {linky_branding.get_instagram_handle()}")
    print(f"   Background Color: {linky_branding.get_background_color()} (Twitter Blue)")
    print(f"   Text Color: {linky_branding.get_text_color()}")
    print(f"   Accent Color: {linky_branding.get_accent_color()} (Bright Blue)")
    print(f"   Theme: Colorful, Twitter-like design")
    
    print("\n📺 TOP MEDIA CHANNEL:")
    print(f"   Channel Name: {topmedia_branding.get_channel_name()}")
    print(f"   Instagram Handle: {topmedia_branding.get_instagram_handle()}")
    print(f"   Background Color: {topmedia_branding.get_background_color()} (Black)")
    print(f"   Text Color: {topmedia_branding.get_text_color()}")
    print(f"   Accent Color: {topmedia_branding.get_accent_color()} (Dark Gray)")
    print(f"   Theme: Minimalist black and white design")
    print()


def main():
    """Main function"""
    print("🚀 Top Media Sample Video Generator\n")
    
    # Create directories
    Config.create_directories()
    
    # Show branding differences
    show_branding_differences()
    
    # Generate sample videos
    success = generate_sample_videos()
    
    if success:
        print("\n📋 Next Steps:")
        print("1. Review the generated videos in output_videos/ folder")
        print("2. Verify the branding differences (colors, handles, themes)")
        print("3. Set up YouTube credentials for Top Media channel")
        print("4. Test posting to both YouTube channels")
        
        print("\n🔧 YouTube Setup Instructions:")
        print("1. Create OAuth credentials for Top Media YouTube channel")
        print("2. Download the client secrets file as 'topmedia_client_secret.json'")
        print("3. Place it in the project root directory")
        print("4. Run the application - it will prompt for Top Media authentication")
        
        return True
    else:
        print("\n❌ Video generation failed. Please check the logs.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
