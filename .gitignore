# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# Environment Variables
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application Specific
instagram_session.json
processed_messages.json
processing_queue.json
post_schedule.json
instagram_post_history.json
usage_tracking.json

# Logs
logs/
*.log

# Temporary files
temp/
*.tmp
*.temp

# Output files
output_videos/*.mp4
output_videos/*.avi
output_videos/*.mov

# Keep sample images but ignore generated videos
!output_videos/*.png
!output_videos/*.jpg

# Audio files (keep structure but ignore large files)
audio/*.mp3
audio/*.wav
audio/*.m4a
!audio/.gitkeep

# Font files (keep the fonts we need)
!fonts/Vazir-Regular.ttf

# Test files
test_*.png
test_*.mp4
*_test_*
multiline_test_*
emoji_*test*
visual_*test*
final_test_*
real_test_*
channel_*
actual_emoji_test_*
character_test_*
quick_test_*

# Emoji cache
emoji_cache/
!emoji_cache/.gitkeep

# Backup files
*.bak
*.backup

# Downloaded assets
vazir-font.zip
dummy_image.jpg

# Media downloads
temp/message_*/
