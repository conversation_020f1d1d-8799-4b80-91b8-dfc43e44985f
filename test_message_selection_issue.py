#!/usr/bin/env python3
"""
Test to demonstrate the message selection issue in instant posting mode
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
import pytz
import random

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

class MockTelegramMessage:
    """Mock Telegram message for testing"""
    def __init__(self, message_id, text, date, reaction_count=0, is_v2ray=False, has_freev2ray=False):
        self.message_id = message_id
        self.text = text
        self.date = date
        self.reaction_count = reaction_count
        self.caption = ""
        self.media_files = []
        self.is_v2ray_config = is_v2ray
        self.has_freev2ray = has_freev2ray
        
        # Mock content type detection
        if self.media_files:
            self.content_type = "text_image"
        else:
            self.content_type = "text_only"

def create_mock_messages():
    """Create mock messages that simulate the current issue"""
    now = datetime.now(pytz.UTC)
    messages = []
    
    # Create 50 messages to simulate a real channel
    for i in range(50):
        message_id = 23000 + i
        
        # Most recent 10 messages (what should be processed)
        if i >= 40:  # Messages 40-49 (newest)
            age_hours = (49 - i) * 0.5  # 0 to 4.5 hours old
            date = now - timedelta(hours=age_hours)
            
            # These should have good reactions since they're recent
            if i >= 45:  # Last 5 messages
                reaction_count = random.randint(15, 30)  # High reactions
                text = f"Recent popular message {message_id} - should be selected!"
            else:
                reaction_count = random.randint(8, 20)  # Medium reactions
                text = f"Recent message {message_id} with decent reactions"
                
        # Older messages (30-40 messages back)
        elif i >= 20:  # Messages 20-39
            age_hours = 24 + (i - 20) * 2  # 24-62 hours old
            date = now - timedelta(hours=age_hours)
            reaction_count = 0  # No reactions (old)
            text = f"Old message {message_id} - no reactions, should NOT be selected"
            
        # Very old messages
        else:  # Messages 0-19
            age_hours = 72 + i * 3  # 72+ hours old
            date = now - timedelta(hours=age_hours)
            reaction_count = 0  # No reactions (very old)
            text = f"Very old message {message_id} - definitely should NOT be selected"
        
        # Add some #freev2ray messages to test filtering
        if i % 7 == 0:  # Every 7th message
            text += " #freev2ray"
            has_freev2ray = True
        else:
            has_freev2ray = False
            
        messages.append(MockTelegramMessage(
            message_id=message_id,
            text=text,
            date=date,
            reaction_count=reaction_count,
            has_freev2ray=has_freev2ray
        ))
    
    # Sort by date (newest first) - this is what Telegram API returns
    messages.sort(key=lambda x: x.date, reverse=True)
    
    return messages

def simulate_telegram_get_recent_messages(all_messages, limit=10, for_polling=True):
    """Simulate the Telegram client's get_recent_messages method"""
    print(f"📡 Simulating Telegram API call: get_recent_messages(limit={limit}, for_polling={for_polling})")
    
    # The issue: Telegram bot API get_updates() doesn't return the most recent messages
    # Instead, it returns messages from the bot's update queue, which might be old
    
    if for_polling:
        # ISSUE: This should return the 10 most recent messages, but instead
        # it's returning messages from 30-40 positions back due to API limitations
        
        # Simulate the problematic behavior
        print("⚠️  ISSUE: Bot API returning old messages instead of recent ones!")
        
        # Instead of taking the first 10 (most recent), we're getting messages from position 30-40
        problematic_start = 30
        problematic_messages = all_messages[problematic_start:problematic_start + limit]
        
        print(f"🔍 Should get messages: {[m.message_id for m in all_messages[:limit]]}")
        print(f"❌ Actually getting messages: {[m.message_id for m in problematic_messages]}")
        
        return problematic_messages
    else:
        # Normal mode works correctly
        return all_messages[:limit]

def simulate_message_filtering(messages):
    """Simulate the message filtering logic"""
    print(f"\n🔍 Filtering {len(messages)} messages...")
    
    filtered_messages = []
    
    for message in messages:
        # Skip #freev2ray messages
        if message.has_freev2ray:
            print(f"  ❌ Skipping #{message.message_id}: #freev2ray")
            continue
            
        # Skip V2ray configs
        if message.is_v2ray_config:
            print(f"  ❌ Skipping #{message.message_id}: V2ray config")
            continue
            
        # Skip already processed (simulate)
        # In real app, this checks against processing_queue
        
        filtered_messages.append(message)
        print(f"  ✅ Keeping #{message.message_id}: {message.text[:50]}...")
    
    print(f"📊 Filtered to {len(filtered_messages)} messages")
    return filtered_messages

def simulate_message_selection(messages):
    """Simulate the _select_best_message logic"""
    print(f"\n🎯 Selecting best message from {len(messages)} candidates...")
    
    if not messages:
        return None
    
    # Sort by reaction count (descending)
    messages_with_reactions = [m for m in messages if m.reaction_count > 0]
    
    if messages_with_reactions:
        # Select the one with most reactions
        best_message = max(messages_with_reactions, key=lambda m: m.reaction_count)
        print(f"✅ Selected message #{best_message.message_id} with {best_message.reaction_count} reactions")
        print(f"📝 Content: {best_message.text}")
        return best_message
    else:
        # No reactions found, select randomly
        selected = random.choice(messages)
        print(f"⚠️  No reactions found, randomly selected message #{selected.message_id}")
        print(f"📝 Content: {selected.text}")
        return selected

def analyze_message_ages(messages):
    """Analyze the age of messages being processed"""
    print(f"\n📅 Analyzing message ages...")
    now = datetime.now(pytz.UTC)
    
    for message in messages:
        age = now - message.date
        age_hours = age.total_seconds() / 3600
        print(f"  Message #{message.message_id}: {age_hours:.1f} hours old, {message.reaction_count} reactions")

async def main():
    """Main test function"""
    print("🔍 LinkInsta Message Selection Issue Test")
    print("=" * 60)
    
    # Create mock messages
    all_messages = create_mock_messages()
    print(f"📊 Created {len(all_messages)} mock messages")
    
    # Show what should happen vs what actually happens
    print(f"\n📈 Most recent 10 messages (what SHOULD be processed):")
    recent_10 = all_messages[:10]
    for i, msg in enumerate(recent_10):
        age_hours = (datetime.now(pytz.UTC) - msg.date).total_seconds() / 3600
        print(f"  {i+1}. Message #{msg.message_id}: {age_hours:.1f}h old, {msg.reaction_count} reactions")
    
    # Simulate the problematic API call
    print(f"\n" + "=" * 60)
    print("🚨 SIMULATING THE CURRENT ISSUE")
    print("=" * 60)
    
    # Get messages using the problematic method
    fetched_messages = simulate_telegram_get_recent_messages(all_messages, limit=10, for_polling=True)
    
    # Analyze what we got
    analyze_message_ages(fetched_messages)
    
    # Filter messages
    filtered_messages = simulate_message_filtering(fetched_messages)
    
    # Select best message
    selected_message = simulate_message_selection(filtered_messages)
    
    # Show the problem
    print(f"\n" + "=" * 60)
    print("📊 ISSUE SUMMARY")
    print("=" * 60)
    
    if selected_message:
        age_hours = (datetime.now(pytz.UTC) - selected_message.date).total_seconds() / 3600
        print(f"❌ Selected message #{selected_message.message_id}")
        print(f"❌ Age: {age_hours:.1f} hours old")
        print(f"❌ Reactions: {selected_message.reaction_count}")
        print(f"❌ Position from latest: ~{30 + fetched_messages.index(selected_message) + 1}")
        
        print(f"\n🎯 What SHOULD have been selected:")
        best_recent = max(recent_10, key=lambda m: m.reaction_count)
        best_age = (datetime.now(pytz.UTC) - best_recent.date).total_seconds() / 3600
        print(f"✅ Message #{best_recent.message_id}")
        print(f"✅ Age: {best_age:.1f} hours old") 
        print(f"✅ Reactions: {best_recent.reaction_count}")
        print(f"✅ Position from latest: {recent_10.index(best_recent) + 1}")
        
        print(f"\n🔧 THE PROBLEM:")
        print(f"- App is processing messages from 30-40 positions back")
        print(f"- These old messages have 0 reactions")
        print(f"- Recent popular messages are being ignored")
        print(f"- This defeats the 'most reacted' selection criteria")
    
    print(f"\n💡 SOLUTION NEEDED:")
    print(f"- Fix Telegram API call to get truly recent messages")
    print(f"- Ensure we're getting the last 10 messages from the channel")
    print(f"- Not messages from the bot's old update queue")

if __name__ == "__main__":
    asyncio.run(main())
