#!/usr/bin/env python3
"""
Test the posting hours fix
"""
import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def enable_posting_hours_restriction():
    """Enable posting hours restriction in .env"""
    env_file = Path('.env')
    
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Read current content
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update setting
    if 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE=false' in content:
        content = content.replace('RESPECT_POSTING_HOURS_IN_INSTANT_MODE=false', 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE=true')
        print("✅ Enabled posting hours restriction")
    elif 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE=true' in content:
        print("✅ Posting hours restriction already enabled")
    else:
        print("❌ Setting not found in .env file")
        return False
    
    # Write back
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def disable_posting_hours_restriction():
    """Disable posting hours restriction in .env"""
    env_file = Path('.env')
    
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Read current content
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update setting
    if 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE=true' in content:
        content = content.replace('RESPECT_POSTING_HOURS_IN_INSTANT_MODE=true', 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE=false')
        print("✅ Disabled posting hours restriction")
    elif 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE=false' in content:
        print("✅ Posting hours restriction already disabled")
    else:
        print("❌ Setting not found in .env file")
        return False
    
    # Write back
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

async def test_posting_outside_hours():
    """Test posting when outside posting hours"""
    print("🧪 Testing posting outside hours...")
    
    # Force reload config
    if 'src.config' in sys.modules:
        del sys.modules['src.config']
    
    from src.config import Config
    from src.main_app import LinkInstaApp
    from src.message_processor import ProcessedMessage, TelegramMessage
    
    # Create test message
    class MockTelegramMessage:
        def __init__(self):
            self.message_id = 99999
            self.text = "Test message for posting hours fix"
            self.text_content = "Test message for posting hours fix"
            self.content_type = "text_only"
            self.media_files = []
            self.is_v2ray_config = False
    
    telegram_msg = MockTelegramMessage()
    processed_msg = ProcessedMessage(telegram_msg)
    
    # Find an existing video
    output_dir = Path("output_videos")
    video_files = list(output_dir.glob("*.mp4"))
    
    if video_files:
        processed_msg.output_video_path = video_files[0]
        processed_msg.video_generated = True
        print(f"📹 Using test video: {video_files[0].name}")
    else:
        print("❌ No video files found for testing")
        return False
    
    # Create app and test
    app = LinkInstaApp()
    
    print(f"🔧 Respect posting hours: {Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE}")
    
    try:
        success = await app.post_instantly_to_platforms(processed_msg)
        
        if success:
            print("✅ Function returned success (no error)")
            return True
        else:
            print("❌ Function returned failure")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

async def main():
    """Main test function"""
    print("🔧 Testing Posting Hours Fix")
    print("=" * 40)
    
    print("\n1. Testing with posting hours restriction ENABLED...")
    enable_posting_hours_restriction()
    
    success1 = await test_posting_outside_hours()
    
    print("\n2. Testing with posting hours restriction DISABLED...")
    disable_posting_hours_restriction()
    
    success2 = await test_posting_outside_hours()
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"✅ With restriction enabled: {'PASS' if success1 else 'FAIL'}")
    print(f"✅ With restriction disabled: {'PASS' if success2 else 'FAIL'}")
    
    if success1 and success2:
        print("\n🎉 Fix successful! No more errors when outside posting hours.")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")
    
    print("\n📝 Current behavior:")
    print("- When outside posting hours + restriction enabled: Content is skipped gracefully")
    print("- When outside posting hours + restriction disabled: Content is posted immediately")
    print("- No more error messages for intended time-based blocking!")

if __name__ == "__main__":
    asyncio.run(main())
