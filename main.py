"""
Main entry point for LinkInsta automation app
"""
import asyncio
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.main_app import LinkInstaApp
from src.utils.logger import logger

def main():
    """Main entry point"""
    try:
        logger.info("Starting LinkInsta automation app...")
        app = LinkInstaApp()
        asyncio.run(app.run())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
