#!/usr/bin/env python3
"""
Test script to verify all scheduler fixes are working
"""
import sys
import json
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_scheduling_logic():
    """Test the scheduling logic fixes"""
    print("🔧 Testing Scheduling Logic Fixes")
    print("=" * 60)
    
    try:
        from src.scheduler import PostScheduler
        
        scheduler = PostScheduler()
        
        # Test optimal posting times
        optimal_times = scheduler._get_optimal_posting_times()
        print(f"Optimal posting times: {optimal_times}")
        print(f"Posting window: {scheduler.posting_start_hour}-{scheduler.posting_end_hour}")
        
        # Test current time
        now_tehran = datetime.now(scheduler.tehran_tz)
        print(f"Current Tehran time: {now_tehran}")
        print(f"Current hour: {now_tehran.hour}")
        print(f"In posting window: {scheduler.posting_start_hour <= now_tehran.hour < scheduler.posting_end_hour}")
        
        # Test next available slot
        next_slot = scheduler._get_next_available_slot()
        next_slot_tehran = pytz.UTC.localize(next_slot).astimezone(scheduler.tehran_tz)
        print(f"Next available slot: {next_slot_tehran}")
        
        return True
        
    except Exception as e:
        print(f"❌ Scheduling logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_processor_fixes():
    """Test message processor fixes"""
    print("\n📝 Testing Message Processor Fixes")
    print("=" * 60)
    
    try:
        from src.message_processor import MessageProcessor
        
        processor = MessageProcessor()
        
        # Test ready for posting logic
        ready_messages = processor.get_ready_for_posting()
        print(f"Messages ready for posting: {len(ready_messages)}")
        
        # Show details of ready messages
        for i, msg in enumerate(ready_messages[:3]):  # Show first 3
            print(f"  Message {i+1}: {msg.telegram_message.message_id}")
            print(f"    Video generated: {msg.video_generated}")
            print(f"    Instagram posted: {msg.instagram_posted}")
            print(f"    Scheduled for posting: {msg.scheduled_for_posting}")
            print(f"    Should skip: {msg.should_skip}")
        
        return True
        
    except Exception as e:
        print(f"❌ Message processor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schedule_execution():
    """Test schedule execution logic"""
    print("\n⚡ Testing Schedule Execution")
    print("=" * 60)
    
    try:
        from src.scheduler import PostScheduler
        
        scheduler = PostScheduler()
        
        print(f"Total scheduled posts: {len(scheduler.scheduled_posts)}")
        
        # Show current schedule status
        now_utc = datetime.utcnow()
        now_tehran = pytz.UTC.localize(now_utc).astimezone(scheduler.tehran_tz)
        
        ready_count = 0
        future_count = 0
        
        for post in scheduler.scheduled_posts:
            if post['status'] != 'scheduled':
                continue
                
            try:
                scheduled_time_naive = datetime.fromisoformat(post['scheduled_time'])
                scheduled_time_utc = pytz.UTC.localize(scheduled_time_naive)
                
                if pytz.UTC.localize(now_utc) >= scheduled_time_utc:
                    ready_count += 1
                else:
                    future_count += 1
            except Exception:
                continue
        
        print(f"Posts ready to execute: {ready_count}")
        print(f"Future posts: {future_count}")
        
        # Test the execution check
        print("\nTesting check_and_execute_posts()...")
        scheduler.check_and_execute_posts()
        
        return True
        
    except Exception as e:
        print(f"❌ Schedule execution test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_timezone_handling():
    """Test timezone handling"""
    print("\n🌍 Testing Timezone Handling")
    print("=" * 60)
    
    try:
        tehran_tz = pytz.timezone('Asia/Tehran')
        
        # Test current time conversions
        now_utc = datetime.utcnow()
        now_tehran = pytz.UTC.localize(now_utc).astimezone(tehran_tz)
        
        print(f"UTC time: {now_utc}")
        print(f"Tehran time: {now_tehran}")
        print(f"Timezone offset: {now_tehran.strftime('%z')}")
        
        # Test scheduling time conversion
        test_times = [
            "2025-07-24T09:00:00",
            "2025-07-24T15:30:00", 
            "2025-07-24T21:45:00"
        ]
        
        for time_str in test_times:
            naive_time = datetime.fromisoformat(time_str)
            utc_time = pytz.UTC.localize(naive_time)
            tehran_time = utc_time.astimezone(tehran_tz)
            
            print(f"Stored: {time_str} -> Tehran: {tehran_time.strftime('%Y-%m-%d %H:%M %Z')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Timezone test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Testing All Scheduler Fixes")
    print("=" * 80)
    
    tests = [
        test_timezone_handling,
        test_scheduling_logic,
        test_message_processor_fixes,
        test_schedule_execution,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All scheduler fixes are working correctly!")
        return True
    else:
        print("⚠️  Some tests failed - issues may still exist")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ All scheduler fixes verified successfully!")
        else:
            print("\n❌ Some scheduler issues detected!")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback
        traceback.print_exc()
