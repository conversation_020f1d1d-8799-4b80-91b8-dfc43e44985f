"""
YouTube client for uploading videos as Shorts
"""
import os
import json
import time
from pathlib import Path
from typing import Optional, Dict, Any

try:
    import googleapiclient.discovery
    import googleapiclient.errors
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
except ImportError as e:
    logger.error(f"Missing YouTube API dependencies: {e}")
    logger.error("Please install: pip install google-api-python-client google-auth google-auth-oauthlib google-auth-httplib2")
    raise

from .config import Config
from .utils.logger import logger


class YouTubeClient:
    """Client for uploading videos to YouTube as Shorts"""

    # YouTube API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/youtube.upload',
        'https://www.googleapis.com/auth/youtube.readonly'
    ]

    def __init__(self, channel_type="linky"):
        """
        Initialize YouTube client for specific channel

        Args:
            channel_type: "linky" for main channel, "topmedia" for Top Media channel
        """
        self.channel_type = channel_type
        self.service = None
        self.credentials = None
        self.is_authenticated = False

        # Rate limiting
        self.last_upload_time = 0
        self.min_interval_seconds = 60  # 1 minute between uploads

        # Set channel-specific configuration
        self._set_channel_config()

    def _set_channel_config(self):
        """Set configuration based on channel type"""
        if self.channel_type == "topmedia":
            self.client_secrets_file = Config.TOPMEDIA_YOUTUBE_CLIENT_SECRETS_FILE
            self.credentials_file = Config.TOPMEDIA_YOUTUBE_CREDENTIALS_FILE
            self.channel_id = Config.TOPMEDIA_YOUTUBE_CHANNEL_ID
            self.default_title = Config.TOPMEDIA_YOUTUBE_DEFAULT_TITLE
            self.default_description = Config.TOPMEDIA_YOUTUBE_DEFAULT_DESCRIPTION
            self.default_tags = Config.TOPMEDIA_YOUTUBE_DEFAULT_TAGS
            self.privacy_status = Config.TOPMEDIA_YOUTUBE_PRIVACY_STATUS
            self.channel_name = "Top Media"
        else:  # linky channel (default)
            self.client_secrets_file = Config.YOUTUBE_CLIENT_SECRETS_FILE
            self.credentials_file = Config.YOUTUBE_CREDENTIALS_FILE
            self.channel_id = Config.YOUTUBE_CHANNEL_ID
            self.default_title = Config.YOUTUBE_DEFAULT_TITLE
            self.default_description = Config.YOUTUBE_DEFAULT_DESCRIPTION
            self.default_tags = Config.YOUTUBE_DEFAULT_TAGS
            self.privacy_status = Config.YOUTUBE_PRIVACY_STATUS
            self.channel_name = "Linky"

    def authenticate(self) -> bool:
        """
        Authenticate with YouTube API using OAuth2
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            creds = None
            
            # Load existing credentials if available
            if self.credentials_file.exists():
                try:
                    creds = Credentials.from_authorized_user_file(
                        str(self.credentials_file),
                        self.SCOPES
                    )
                    logger.info(f"Loaded existing {self.channel_name} YouTube credentials")
                except Exception as e:
                    logger.warning(f"Failed to load existing {self.channel_name} credentials: {e}")
            
            # If there are no (valid) credentials available, let the user log in
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    try:
                        creds.refresh(Request())
                        logger.info(f"Refreshed {self.channel_name} YouTube credentials")
                    except Exception as e:
                        logger.warning(f"Failed to refresh {self.channel_name} credentials: {e}")
                        creds = None

                if not creds:
                    if not self.client_secrets_file.exists():
                        logger.error(f"{self.channel_name} YouTube client secrets file not found: {self.client_secrets_file}")
                        logger.error("Please download the OAuth2 client secrets file from Google Cloud Console")
                        return False

                    flow = InstalledAppFlow.from_client_secrets_file(
                        str(self.client_secrets_file),
                        self.SCOPES
                    )
                    creds = flow.run_local_server(port=0)
                    logger.info(f"Completed {self.channel_name} YouTube OAuth2 flow")

                # Save the credentials for the next run
                with open(self.credentials_file, 'w') as token:
                    token.write(creds.to_json())
                logger.info(f"Saved {self.channel_name} YouTube credentials to {self.credentials_file}")
            
            # Build the YouTube service
            self.service = googleapiclient.discovery.build(
                'youtube', 'v3', credentials=creds
            )
            self.credentials = creds
            self.is_authenticated = True

            logger.info(f"✅ {self.channel_name} YouTube authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"YouTube authentication failed: {e}")
            return False
    
    def _can_upload_now(self) -> bool:
        """Check if we can upload now based on rate limiting"""
        return time.time() - self.last_upload_time >= self.min_interval_seconds
    
    def upload_video(self, video_path: Path, title: str = None, description: str = None, 
                    tags: list = None, privacy_status: str = None) -> Optional[str]:
        """
        Upload a video to YouTube as a Short
        
        Args:
            video_path: Path to the video file
            title: Video title (optional, uses default if not provided)
            description: Video description (optional, uses default if not provided)
            tags: List of tags (optional, uses default if not provided)
            privacy_status: Privacy status (public, private, unlisted)
            
        Returns:
            Video ID if successful, None otherwise
        """
        try:
            if not self.is_authenticated:
                if not self.authenticate():
                    logger.error("Cannot upload video: not authenticated")
                    return None
            
            if not self._can_upload_now():
                wait_time = self.min_interval_seconds - (time.time() - self.last_upload_time)
                logger.warning(f"Rate limit: need to wait {wait_time:.1f} seconds before uploading")
                return None
            
            if not video_path.exists():
                logger.error(f"Video file not found: {video_path}")
                return None
            
            # Prepare video metadata
            title = title or self.default_title or f"{self.channel_name} Content - {int(time.time())}"
            description = description or self.default_description
            tags = tags or self.default_tags
            privacy_status = privacy_status or self.privacy_status
            
            # Add Shorts indicator to title and description
            if "#Shorts" not in title:
                title = f"{title} #Shorts"
            
            if "#Shorts" not in description:
                description = f"{description}\n\n#Shorts"
            
            # Add Farsi note to description
            farsi_note = "📱 این محتوا بر اساس توییت‌های شما ساخته شده است"
            description = f"{description}\n\n{farsi_note}"
            
            body = {
                'snippet': {
                    'title': title,
                    'description': description,
                    'tags': tags,
                    'categoryId': '22'  # People & Blogs category
                },
                'status': {
                    'privacyStatus': privacy_status,
                    'selfDeclaredMadeForKids': False
                }
            }
            
            logger.info(f"Uploading video to {self.channel_name} YouTube: {video_path.name}")
            logger.info(f"Title: {title}")
            logger.info(f"Privacy: {privacy_status}")
            
            # Call the API's videos.insert method to create and upload the video
            insert_request = self.service.videos().insert(
                part=','.join(body.keys()),
                body=body,
                media_body=googleapiclient.http.MediaFileUpload(
                    str(video_path),
                    chunksize=-1,
                    resumable=True
                )
            )
            
            # Execute the upload
            response = None
            error = None
            retry = 0
            
            while response is None:
                try:
                    status, response = insert_request.next_chunk()
                    if status:
                        logger.info(f"Upload progress: {int(status.progress() * 100)}%")
                except googleapiclient.errors.HttpError as e:
                    if e.resp.status in [500, 502, 503, 504]:
                        # Retriable error
                        error = f"A retriable HTTP error {e.resp.status} occurred:\n{e.content}"
                        logger.warning(error)
                    else:
                        # Non-retriable error
                        error = f"A non-retriable HTTP error {e.resp.status} occurred:\n{e.content}"
                        logger.error(error)
                        break
                except Exception as e:
                    error = f"An unexpected error occurred: {e}"
                    logger.error(error)
                    break
            
            if response is not None:
                video_id = response['id']
                logger.info(f"✅ Successfully uploaded to {self.channel_name} YouTube: {video_id}")
                logger.info(f"🔗 Video URL: https://www.youtube.com/watch?v={video_id}")

                # Update rate limiting
                self.last_upload_time = time.time()

                return video_id
            else:
                logger.error(f"{self.channel_name} YouTube upload failed: {error}")
                return None
                
        except Exception as e:
            logger.error(f"Error uploading video to {self.channel_name} YouTube: {e}")
            return None
    
    def get_channel_info(self) -> Optional[Dict[str, Any]]:
        """
        Get information about the authenticated channel
        
        Returns:
            Channel information dict or None if failed
        """
        try:
            if not self.is_authenticated:
                if not self.authenticate():
                    return None
            
            request = self.service.channels().list(
                part='snippet,statistics',
                mine=True
            )
            response = request.execute()
            
            if response['items']:
                channel = response['items'][0]
                return {
                    'id': channel['id'],
                    'title': channel['snippet']['title'],
                    'description': channel['snippet']['description'],
                    'subscriber_count': channel['statistics'].get('subscriberCount', 'Hidden'),
                    'video_count': channel['statistics'].get('videoCount', '0'),
                    'view_count': channel['statistics'].get('viewCount', '0')
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting channel info: {e}")
            return None
