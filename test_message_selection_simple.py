#!/usr/bin/env python3
"""
Simple test to show message selection for user approval
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def show_selected_message():
    """Show the message that would be selected for processing"""
    print("🎯 Message Selection Test")
    print("=" * 50)
    
    try:
        from src.telegram_client import TelegramClient
        
        # Create Bot API client with longer timeout
        bot_client = TelegramClient()
        
        print("🔄 Connecting to Telegram...")
        if await bot_client.initialize():
            print("✅ Connected successfully")
            
            # Get recent messages with your like
            print("\n📡 Getting recent messages (including your like)...")
            messages = await bot_client.get_recent_messages(limit=10, for_polling=True)
            
            if messages:
                print(f"✅ Retrieved {len(messages)} messages")
                
                # Show all messages with reaction counts
                tehran_tz = pytz.timezone('Asia/Tehran')
                now_tehran = datetime.now(tehran_tz)
                
                print(f"\n📊 Recent Messages Analysis:")
                print("=" * 70)
                
                best_message = None
                max_reactions = 0
                
                for i, msg in enumerate(messages):
                    msg_tehran = msg.date.astimezone(tehran_tz)
                    age = now_tehran - msg_tehran
                    age_hours = age.total_seconds() / 3600
                    
                    reaction_count = getattr(msg, 'reaction_count', 0)
                    
                    print(f"  {i+1}. Message #{msg.message_id}")
                    print(f"     Time: {msg_tehran.strftime('%H:%M %d/%m')} ({age_hours:.1f}h ago)")
                    print(f"     Reactions: {reaction_count}")
                    print(f"     Text: {(msg.text or msg.caption or 'No text')[:60]}...")
                    
                    if reaction_count > max_reactions:
                        max_reactions = reaction_count
                        best_message = msg
                    
                    if reaction_count > 0:
                        print(f"     🔥 HAS REACTIONS!")
                    print()
                
                # Show which message would be selected
                if best_message and max_reactions > 0:
                    print(f"🏆 WOULD SELECT MESSAGE WITH MOST REACTIONS:")
                    print(f"📱 Message ID: #{best_message.message_id}")
                    print(f"🔥 Reactions: {max_reactions}")
                    print(f"⏰ Time: {best_message.date.astimezone(tehran_tz).strftime('%H:%M %d/%m')}")
                    print(f"📝 Content: {(best_message.text or best_message.caption or 'No text')}")
                    print()
                    print("❓ Do you approve this message selection?")
                    print("👍 This message has the most reactions from recent posts")
                    
                    return best_message
                else:
                    # Show what would happen with no reactions
                    if messages:
                        random_msg = messages[0]  # Would pick first/random
                        print(f"📝 NO REACTIONS DETECTED - Would select randomly:")
                        print(f"📱 Message ID: #{random_msg.message_id}")
                        print(f"🔥 Reactions: 0")
                        print(f"⏰ Time: {random_msg.date.astimezone(tehran_tz).strftime('%H:%M %d/%m')}")
                        print(f"📝 Content: {(random_msg.text or random_msg.caption or 'No text')}")
                        print()
                        print("💡 Note: Your like might not be visible to the bot API yet")
                        print("❓ Do you approve this random selection?")
                        
                        return random_msg
                    
            else:
                print("❌ No messages retrieved")
                return None
        else:
            print("❌ Failed to connect to Telegram")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        try:
            await bot_client.close()
        except:
            pass

async def main():
    """Main function"""
    print("🚀 LinkInsta Message Selection for Approval")
    print("=" * 60)
    
    print("🎯 Goal: Show you the message that would be selected")
    print("👍 You mentioned you just liked a recent post")
    print("📱 Let's see if the app detects it and selects it")
    print()
    
    selected = await show_selected_message()
    
    if selected:
        print("\n" + "=" * 60)
        print("✅ MESSAGE SELECTION READY FOR YOUR APPROVAL")
        print("=" * 60)
        print("📋 Please review the selected message above")
        print("👍 If you approve, the app will process this message")
        print("🎬 It will create a video and post to Instagram + YouTube")
        print("📝 With Farsi attribution about tweet-based content")
    else:
        print("\n❌ Could not retrieve messages for selection")
        print("🔧 This might be a temporary connection issue")
        print("💡 Try running the app again in a moment")

if __name__ == "__main__":
    asyncio.run(main())
