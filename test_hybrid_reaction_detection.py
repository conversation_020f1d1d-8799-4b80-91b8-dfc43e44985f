#!/usr/bin/env python3
"""
Test hybrid approach: Bot API + User API for real reaction detection
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_hybrid_reaction_detection():
    """Test the hybrid client for real reaction detection"""
    print("🔥 Testing Hybrid Reaction Detection")
    print("=" * 60)
    
    try:
        from src.hybrid_telegram_client import HybridTelegramClient
        
        # Create hybrid client
        hybrid_client = HybridTelegramClient()
        
        print("🔄 Initializing hybrid client...")
        if await hybrid_client.initialize():
            print("✅ Hybrid client initialized")
            
            # Get recent messages with real reaction data
            print("\n📡 Getting messages with REAL reaction data...")
            messages = await hybrid_client.get_recent_messages(limit=5, for_polling=True)
            
            if messages:
                print(f"✅ Retrieved {len(messages)} messages with reaction enhancement")
                
                # Analyze each message with real reactions
                tehran_tz = pytz.timezone('Asia/Tehran')
                now_tehran = datetime.now(tehran_tz)
                
                print(f"\n🔥 REAL REACTION DATA:")
                print("=" * 80)
                
                messages_with_reactions = 0
                total_reactions = 0
                best_message = None
                best_reactions = 0
                
                for i, msg in enumerate(messages):
                    msg_tehran = msg.date.astimezone(tehran_tz)
                    age = now_tehran - msg_tehran
                    age_hours = age.total_seconds() / 3600
                    
                    reaction_count = getattr(msg, 'reaction_count', 0)
                    
                    print(f"  {i+1:2d}. Message #{msg.message_id}")
                    print(f"      Time: {msg_tehran.strftime('%H:%M %d/%m')} ({age_hours:.1f}h ago)")
                    print(f"      Reactions: {reaction_count} (REAL DATA!)")
                    print(f"      Text: {(msg.text or msg.caption or 'No text')[:60]}...")
                    
                    if reaction_count > 0:
                        messages_with_reactions += 1
                        total_reactions += reaction_count
                        print(f"      🔥 HAS {reaction_count} REAL REACTIONS!")
                        
                        if reaction_count > best_reactions:
                            best_message = msg
                            best_reactions = reaction_count
                    else:
                        print(f"      📝 No reactions")
                    print()
                
                # Summary
                print(f"📈 HYBRID CLIENT RESULTS:")
                print(f"   Total messages retrieved: {len(messages)}")
                print(f"   Messages with real reactions: {messages_with_reactions}")
                print(f"   Total real reactions detected: {total_reactions}")
                
                if best_message:
                    print(f"\n🏆 BEST MESSAGE (Most Reactions):")
                    print(f"   Message ID: #{best_message.message_id}")
                    print(f"   Real Reactions: {best_reactions}")
                    print(f"   Content: {(best_message.text or best_message.caption or 'No text')[:100]}...")
                    
                    # Check if this is message #22949 with 2 reactions
                    if best_message.message_id == 22949 and best_reactions == 2:
                        print(f"   🎯 PERFECT! This is the message you mentioned with 2 reactions!")
                    
                    return {
                        'best_id': best_message.message_id,
                        'best_reactions': best_reactions,
                        'total_messages': len(messages),
                        'messages_with_reactions': messages_with_reactions
                    }
                else:
                    print(f"   📝 No messages with reactions found")
                    return None
                
            else:
                print("❌ No messages retrieved")
                return None
        else:
            print("❌ Failed to initialize hybrid client")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        try:
            await hybrid_client.close()
        except:
            pass

async def test_message_selection_with_real_reactions():
    """Test message selection with real reaction data"""
    print("\n🎯 Testing Message Selection with Real Reactions")
    print("=" * 60)
    
    try:
        from src.message_processor import MessageProcessor
        
        # Create message processor (now uses hybrid client)
        processor = MessageProcessor()
        
        print("🔄 Testing message selection with REAL reaction data...")
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected = processed_messages[0]
            msg_id = selected.telegram_message.message_id
            reactions = getattr(selected.telegram_message, 'reaction_count', 0)
            
            print(f"✅ HYBRID SELECTION RESULT:")
            print(f"📱 Message ID: #{msg_id}")
            print(f"🔥 Real Reactions: {reactions}")
            print(f"📝 Content: {selected.text_content[:100]}...")
            
            # Check if it selected the message with most reactions
            if reactions > 0:
                print(f"🏆 EXCELLENT! Selected message with {reactions} REAL reactions!")
                print(f"✅ No more fake reaction counting!")
            else:
                print(f"📝 Selected message with 0 reactions (may be most recent)")
            
            return {
                'selected_id': msg_id,
                'selected_reactions': reactions
            }
        else:
            print("❌ No message selected")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """Main test function"""
    print("🚀 Hybrid Reaction Detection Test")
    print("=" * 70)
    
    print("🎯 HYBRID APPROACH:")
    print("✅ Bot API: Message retrieval (prevents duplication)")
    print("✅ User API: Real reaction counting (no more fake data)")
    print("✅ Best of both worlds!")
    print()
    
    # Test 1: Hybrid reaction detection
    hybrid_result = await test_hybrid_reaction_detection()
    
    # Test 2: Message selection with real reactions
    selection_result = await test_message_selection_with_real_reactions()
    
    # Analysis
    print("\n" + "=" * 70)
    print("📊 HYBRID SYSTEM RESULTS")
    print("=" * 70)
    
    if hybrid_result:
        print(f"✅ Hybrid client working!")
        print(f"📱 Retrieved {hybrid_result['total_messages']} messages")
        print(f"🔥 {hybrid_result['messages_with_reactions']} messages have real reactions")
        print(f"🏆 Best message: #{hybrid_result['best_id']} with {hybrid_result['best_reactions']} reactions")
        
        if hybrid_result['best_id'] == 22949 and hybrid_result['best_reactions'] == 2:
            print(f"🎯 PERFECT! Found your message #22949 with 2 reactions!")
    
    if selection_result:
        print(f"\n🎯 Message Selection:")
        print(f"📱 Selected: #{selection_result['selected_id']}")
        print(f"🔥 Reactions: {selection_result['selected_reactions']}")
        
        if selection_result['selected_reactions'] > 0:
            print(f"🏆 SUCCESS! Now selecting based on REAL reactions!")
        
    # Final recommendation
    print(f"\n💡 RECOMMENDATION:")
    if hybrid_result and hybrid_result['messages_with_reactions'] > 0:
        print(f"🎉 HYBRID APPROACH WORKING!")
        print(f"✅ Real reaction data detected")
        print(f"✅ Can now select most popular posts")
        print(f"✅ No more Bot API limitations for reactions")
    else:
        print(f"⚠️  User API may need authentication")
        print(f"💡 Check if telethon is installed: pip install telethon")

if __name__ == "__main__":
    asyncio.run(main())
