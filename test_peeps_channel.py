#!/usr/bin/env python3
"""
Test posting to the correct Peeps (@topmedii) channel
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from youtube_client import YouTubeClient
from video_generators.text_video_generator import TextVideoGenerator
from utils.logger import logger


def verify_peeps_channel_access():
    """Verify we can access the Peeps channel correctly"""
    print("🔍 Verifying Peeps Channel Access...\n")
    
    try:
        topmedia_client = YouTubeClient("topmedia")
        
        print(f"📋 Configuration:")
        print(f"   Client Secrets: {topmedia_client.client_secrets_file.name}")
        print(f"   Target Channel ID: {Config.TOPMEDIA_YOUTUBE_CHANNEL_ID}")
        print(f"   Expected Channel: Peeps (@topmedii)")
        
        # Check if new client secrets file exists
        if not topmedia_client.client_secrets_file.exists():
            print(f"❌ New client secrets file not found!")
            print(f"   Expected: {topmedia_client.client_secrets_file}")
            print(f"   Please download the OAuth credentials file with Client ID:")
            print(f"   444051048706-vcnnbk2ngd66i4o9kpj6h23paa41jlr1.apps.googleusercontent.com")
            return False
        
        print(f"✅ Client secrets file found")
        
        # Authenticate with new credentials
        print(f"\n🔑 Authenticating with new OAuth credentials...")
        auth_success = topmedia_client.authenticate()
        
        if not auth_success:
            print(f"❌ Authentication failed")
            return False
        
        print(f"✅ Authentication successful!")
        
        # Check accessible channels
        print(f"\n📺 Checking accessible channels...")
        
        request = topmedia_client.service.channels().list(
            part='snippet,statistics',
            mine=True,
            maxResults=50
        )
        response = request.execute()
        
        print(f"✅ Found {len(response.get('items', []))} accessible channel(s):")
        
        target_channel_id = Config.TOPMEDIA_YOUTUBE_CHANNEL_ID
        peeps_found = False
        
        for i, channel in enumerate(response.get('items', []), 1):
            channel_id = channel['id']
            channel_title = channel['snippet']['title']
            custom_url = channel['snippet'].get('customUrl', 'No handle')
            
            is_peeps = channel_id == target_channel_id
            if is_peeps:
                peeps_found = True
            
            status = "🎯 PEEPS CHANNEL" if is_peeps else "📺"
            
            print(f"   {status} {i}. {channel_title}")
            print(f"      Channel ID: {channel_id}")
            print(f"      Handle: @{custom_url}")
            print()
        
        if peeps_found:
            print(f"✅ SUCCESS: Peeps channel found in accessible channels!")
            print(f"   This means videos will upload to the correct channel.")
        else:
            print(f"⚠️ Peeps channel not found in 'mine' list")
            print(f"   Checking direct access...")
            
            # Try direct access
            direct_request = topmedia_client.service.channels().list(
                part='snippet',
                id=target_channel_id
            )
            direct_response = direct_request.execute()
            
            if direct_response.get('items'):
                channel = direct_response['items'][0]
                print(f"✅ Can access Peeps channel directly: {channel['snippet']['title']}")
                peeps_found = True
            else:
                print(f"❌ Cannot access Peeps channel")
        
        return peeps_found
        
    except Exception as e:
        print(f"❌ Error verifying channel access: {e}")
        return False


def create_and_upload_test_video():
    """Create and upload test video to Peeps channel"""
    print("\n🎬 Creating Test Video for Peeps Channel...\n")
    
    try:
        # Generate test video with Top Media branding
        test_text = """🔥 تست کانال Peeps (@topmedii) 🔥

این ویدیو تست برای کانال Peeps است.

✅ کانال: Peeps
✅ هندل: @topmedii  
✅ برندینگ: @topmedi
✅ طراحی: سیاه و سفید

#Peeps #TopMedi #Test #YouTube"""
        
        topmedia_generator = TextVideoGenerator("topmedia")
        test_output = Config.OUTPUT_DIR / "peeps_channel_test.mp4"
        
        print(f"📹 Generating video with @topmedi branding...")
        success = topmedia_generator.generate_video(test_text, test_output)
        
        if not success:
            print("❌ Video generation failed")
            return False
        
        print(f"✅ Video generated: {test_output.name}")
        print(f"   Branding: {topmedia_generator.branding.get_brand_text()}")
        print(f"   Theme: Black/White")
        
        # Upload to YouTube
        topmedia_client = YouTubeClient("topmedia")
        
        title = "🔥 تست کانال Peeps - @topmedii Test Video"
        description = """این ویدیو تست برای کانال Peeps (@topmedii) است.

🎯 تست‌های انجام شده:
✅ برندینگ @topmedi
✅ طراحی سیاه و سفید
✅ انتشار روی کانال صحیح
✅ سیستم خودکار تولید محتوا

📱 این محتوا بر اساس سیستم خودکار Top Media تولید شده است

#Peeps #TopMedi #Test #YouTube #Shorts"""
        
        print(f"\n📤 Uploading to Peeps channel...")
        print(f"   Title: {title}")
        print(f"   Target: Peeps (@topmedii)")
        
        video_id = topmedia_client.upload_video(
            video_path=test_output,
            title=title,
            description=description,
            tags=["peeps", "topmedi", "test", "shorts"],
            privacy_status="public"
        )
        
        if video_id:
            print(f"\n🎉 SUCCESS! Video uploaded!")
            print(f"📺 Video ID: {video_id}")
            print(f"🔗 Video URL: https://www.youtube.com/watch?v={video_id}")
            
            # Verify upload location
            print(f"\n🔍 Verifying upload location...")
            
            video_request = topmedia_client.service.videos().list(
                part='snippet',
                id=video_id
            )
            video_response = video_request.execute()
            
            if video_response.get('items'):
                video_info = video_response['items'][0]
                uploaded_channel_id = video_info['snippet']['channelId']
                uploaded_channel_title = video_info['snippet']['channelTitle']
                
                print(f"📊 Upload Verification:")
                print(f"   Uploaded to: {uploaded_channel_title}")
                print(f"   Channel ID: {uploaded_channel_id}")
                print(f"   Expected ID: {Config.TOPMEDIA_YOUTUBE_CHANNEL_ID}")
                
                if uploaded_channel_id == Config.TOPMEDIA_YOUTUBE_CHANNEL_ID:
                    print(f"✅ PERFECT! Video posted to correct Peeps channel!")
                    print(f"🎯 Channel URL: https://www.youtube.com/@topmedii")
                    return True
                else:
                    print(f"❌ Wrong channel! Posted to: {uploaded_channel_title}")
                    return False
            else:
                print(f"⚠️ Could not verify upload location")
                return True
        else:
            print(f"❌ Upload failed")
            return False
            
    except Exception as e:
        print(f"❌ Error in test: {e}")
        return False


def main():
    """Run Peeps channel test"""
    print("🚀 PEEPS CHANNEL (@topmedii) TEST\n")
    print("=" * 60)
    
    # Step 1: Verify channel access
    print("STEP 1: Verify Channel Access")
    access_ok = verify_peeps_channel_access()
    
    if not access_ok:
        print("\n❌ Cannot access Peeps channel")
        print("\n💡 Next steps:")
        print("   1. Make sure you downloaded the OAuth file with Client ID:")
        print("      444051048706-vcnnbk2ngd66i4o9kpj6h23paa41jlr1.apps.googleusercontent.com")
        print("   2. Save it as: client_secret_444051048706-vcnnbk2ngd66i4o9kpj6h23paa41jlr1.apps.googleusercontent.com.json")
        print("   3. Place it in the project root directory")
        return False
    
    # Step 2: Create and upload test video
    print("\n" + "=" * 60)
    print("STEP 2: Create and Upload Test Video")
    upload_ok = create_and_upload_test_video()
    
    # Final result
    print("\n" + "=" * 60)
    if upload_ok:
        print("🎉 COMPLETE SUCCESS!")
        print("\n✅ Peeps channel integration working perfectly!")
        print("\n📋 Verified:")
        print("   ✅ OAuth authentication with correct account")
        print("   ✅ Video generation with @topmedi branding")
        print("   ✅ Black/white theme applied correctly")
        print("   ✅ Video uploaded to correct Peeps channel")
        print("\n🔗 Check your video at: https://www.youtube.com/@topmedii")
        print("📺 Channel: Peeps (@topmedii)")
        return True
    else:
        print("❌ Test failed. Check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
