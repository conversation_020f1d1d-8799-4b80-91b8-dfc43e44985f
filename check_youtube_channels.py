#!/usr/bin/env python3
"""
Check available YouTube channels and ensure we're posting to the correct one
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from youtube_client import YouTubeClient
from utils.logger import logger


def check_available_channels():
    """Check all available YouTube channels for the authenticated user"""
    print("📺 Checking Available YouTube Channels...\n")
    
    try:
        # Initialize Top Media YouTube client
        topmedia_client = YouTubeClient("topmedia")
        
        # Authenticate
        if not topmedia_client.authenticate():
            print("❌ Authentication failed")
            return False
        
        # Get all channels the user has access to
        print("🔍 Retrieving all accessible channels...")
        
        request = topmedia_client.service.channels().list(
            part='snippet,statistics,brandingSettings',
            mine=True,
            maxResults=50
        )
        response = request.execute()
        
        if not response.get('items'):
            print("❌ No channels found")
            return False
        
        print(f"✅ Found {len(response['items'])} channel(s):\n")
        
        target_channel_id = Config.TOPMEDIA_YOUTUBE_CHANNEL_ID
        target_found = False
        
        for i, channel in enumerate(response['items'], 1):
            channel_id = channel['id']
            channel_title = channel['snippet']['title']
            channel_handle = channel['snippet'].get('customUrl', 'No handle')
            subscriber_count = channel['statistics'].get('subscriberCount', 'Hidden')
            
            is_target = channel_id == target_channel_id
            if is_target:
                target_found = True
            
            status = "🎯 TARGET CHANNEL" if is_target else "📺"
            
            print(f"{status} Channel {i}:")
            print(f"   Title: {channel_title}")
            print(f"   Channel ID: {channel_id}")
            print(f"   Handle: @{channel_handle}")
            print(f"   Subscribers: {subscriber_count}")
            print(f"   URL: https://www.youtube.com/channel/{channel_id}")
            print()
        
        print(f"📋 Configuration Check:")
        print(f"   Target Channel ID: {target_channel_id}")
        print(f"   Target Found: {'✅ YES' if target_found else '❌ NO'}")
        
        if not target_found:
            print(f"\n⚠️ WARNING: Target channel not found in accessible channels!")
            print(f"   This means the authenticated account doesn't have access to:")
            print(f"   Channel ID: {target_channel_id}")
            print(f"   Channel URL: https://www.youtube.com/channel/{target_channel_id}")
            print(f"\n💡 Solutions:")
            print(f"   1. Make sure you're logged in with the correct Google account")
            print(f"   2. Ensure the account has manager/owner access to Top Media channel")
            print(f"   3. Check if the channel ID is correct")
        
        return target_found
        
    except Exception as e:
        print(f"❌ Error checking channels: {e}")
        return False


def test_channel_selection():
    """Test if we can select and use the correct channel"""
    print("🎯 Testing Channel Selection...\n")
    
    try:
        topmedia_client = YouTubeClient("topmedia")
        
        if not topmedia_client.authenticate():
            print("❌ Authentication failed")
            return False
        
        # Try to get specific channel info
        target_channel_id = Config.TOPMEDIA_YOUTUBE_CHANNEL_ID
        
        print(f"🔍 Testing access to target channel: {target_channel_id}")
        
        request = topmedia_client.service.channels().list(
            part='snippet,statistics',
            id=target_channel_id
        )
        response = request.execute()
        
        if response.get('items'):
            channel = response['items'][0]
            print(f"✅ Successfully accessed target channel:")
            print(f"   Title: {channel['snippet']['title']}")
            print(f"   Channel ID: {channel['id']}")
            print(f"   Description: {channel['snippet']['description'][:100]}...")
            return True
        else:
            print(f"❌ Cannot access target channel")
            print(f"   Channel ID: {target_channel_id}")
            print(f"   This usually means:")
            print(f"   - Wrong channel ID")
            print(f"   - No access permissions")
            print(f"   - Channel doesn't exist")
            return False
            
    except Exception as e:
        print(f"❌ Error testing channel selection: {e}")
        return False


def main():
    """Run channel verification"""
    print("🚀 YOUTUBE CHANNEL VERIFICATION\n")
    print("=" * 60)
    
    # Step 1: Check available channels
    print("STEP 1: Check Available Channels")
    channels_ok = check_available_channels()
    
    print("\n" + "=" * 60)
    print("STEP 2: Test Target Channel Access")
    target_ok = test_channel_selection()
    
    print("\n" + "=" * 60)
    print("📊 RESULTS:")
    
    if channels_ok and target_ok:
        print("✅ SUCCESS: Everything is configured correctly!")
        print("\n🎯 Top Media channel is accessible and ready for posting")
        print(f"📺 Channel: https://www.youtube.com/@topmedii")
        print(f"🆔 Channel ID: {Config.TOPMEDIA_YOUTUBE_CHANNEL_ID}")
    elif channels_ok and not target_ok:
        print("⚠️ PARTIAL: Can access channels but not the target one")
        print("\n💡 Next steps:")
        print("   1. Verify the channel ID is correct")
        print("   2. Ensure your account has manager access to Top Media")
        print("   3. Try re-authenticating with the correct account")
    else:
        print("❌ FAILED: Cannot access YouTube channels")
        print("\n💡 Next steps:")
        print("   1. Check YouTube Data API v3 is enabled")
        print("   2. Verify OAuth credentials are correct")
        print("   3. Ensure proper redirect URIs are configured")
    
    return channels_ok and target_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
