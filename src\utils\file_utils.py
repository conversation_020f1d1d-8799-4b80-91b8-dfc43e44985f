"""
File utilities for safe file operations and cleanup
"""
import os
import time
import gc
import psutil
from pathlib import Path
from typing import Optional, List
import logging

logger = logging.getLogger(__name__)


def is_file_in_use(file_path: Path) -> bool:
    """
    Check if a file is currently being used by any process
    
    Args:
        file_path: Path to the file to check
        
    Returns:
        True if file is in use, False otherwise
    """
    try:
        if not file_path.exists():
            return False
            
        # Try to open the file in exclusive mode
        try:
            with open(file_path, 'r+b') as f:
                pass
            return False
        except (PermissionError, OSError):
            return True
            
    except Exception as e:
        logger.debug(f"Error checking if file is in use {file_path}: {e}")
        return True


def get_processes_using_file(file_path: Path) -> List[str]:
    """
    Get list of process names that are using the specified file
    
    Args:
        file_path: Path to the file to check
        
    Returns:
        List of process names using the file
    """
    processes = []
    try:
        file_path_str = str(file_path.resolve())
        
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                for file_info in proc.open_files():
                    if file_info.path == file_path_str:
                        processes.append(proc.info['name'])
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
                
    except Exception as e:
        logger.debug(f"Error getting processes using file {file_path}: {e}")
        
    return processes


def force_garbage_collection():
    """Force garbage collection to release any lingering file handles"""
    try:
        gc.collect()
        time.sleep(0.1)  # Small delay to allow cleanup
    except Exception as e:
        logger.debug(f"Error during garbage collection: {e}")


def safe_delete_file(file_path: Path, max_retries: int = 10, initial_delay: float = 1.0) -> bool:
    """
    Safely delete a file with retry logic and exponential backoff
    
    Args:
        file_path: Path to the file to delete
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds
        
    Returns:
        True if file was successfully deleted, False otherwise
    """
    if not file_path.exists():
        logger.debug(f"File already deleted or doesn't exist: {file_path}")
        return True
        
    # Force garbage collection first
    force_garbage_collection()
    
    delay = initial_delay
    last_error = None
    
    for attempt in range(max_retries):
        try:
            # Check if file is in use
            if is_file_in_use(file_path):
                if attempt == 0:
                    # Log which processes are using the file on first attempt
                    using_processes = get_processes_using_file(file_path)
                    if using_processes:
                        logger.warning(f"File {file_path.name} is in use by: {', '.join(using_processes)}")
                    else:
                        logger.warning(f"File {file_path.name} is in use by unknown process")
                
                if attempt < max_retries - 1:
                    logger.debug(f"File in use, waiting {delay:.1f}s before retry {attempt + 1}/{max_retries}")
                    time.sleep(delay)
                    delay = min(delay * 1.5, 30.0)  # Exponential backoff, max 30 seconds
                    continue
            
            # Try to delete the file
            file_path.unlink()
            logger.info(f"🗑️  Successfully deleted file: {file_path.name}")
            return True
            
        except FileNotFoundError:
            # File was already deleted
            logger.debug(f"File already deleted: {file_path}")
            return True
            
        except (PermissionError, OSError) as e:
            last_error = e
            if attempt < max_retries - 1:
                logger.debug(f"Delete attempt {attempt + 1} failed: {e}. Retrying in {delay:.1f}s...")
                time.sleep(delay)
                delay = min(delay * 1.5, 30.0)  # Exponential backoff
                
                # Force garbage collection between retries
                force_garbage_collection()
            else:
                logger.error(f"Failed to delete file {file_path} after {max_retries} attempts: {e}")
                
        except Exception as e:
            last_error = e
            logger.error(f"Unexpected error deleting file {file_path}: {e}")
            break
    
    # Final attempt with more aggressive cleanup
    try:
        logger.debug(f"Final cleanup attempt for {file_path}")
        force_garbage_collection()
        time.sleep(2.0)  # Longer wait
        
        if file_path.exists():
            file_path.unlink()
            logger.info(f"🗑️  Successfully deleted file on final attempt: {file_path.name}")
            return True
            
    except Exception as e:
        logger.error(f"Final cleanup attempt failed for {file_path}: {e}")
    
    # Log final failure with helpful information
    if file_path.exists():
        using_processes = get_processes_using_file(file_path)
        if using_processes:
            logger.error(f"File deletion failed - still in use by: {', '.join(using_processes)}")
        else:
            logger.error(f"File deletion failed - unknown reason: {last_error}")
    
    return False


def cleanup_video_file_robust(video_path: Path, delay_before_cleanup: float = 5.0) -> bool:
    """
    Robustly clean up a video file with proper delay and retry logic

    Args:
        video_path: Path to the video file to clean up
        delay_before_cleanup: Delay in seconds before attempting cleanup

    Returns:
        True if cleanup was successful, False otherwise
    """
    try:
        if not video_path.exists():
            logger.debug(f"Video file doesn't exist for cleanup: {video_path}")
            return True

        # Initial delay to allow any processes to release the file
        if delay_before_cleanup > 0:
            logger.debug(f"Waiting {delay_before_cleanup}s before cleanup of {video_path.name}")
            time.sleep(delay_before_cleanup)

        # Attempt robust deletion
        success = safe_delete_file(video_path)

        if success:
            logger.info(f"🗑️  Cleaned up video file after posting: {video_path.name}")
        else:
            logger.warning(f"Could not clean up video file: {video_path.name}")

        return success

    except Exception as e:
        logger.error(f"Error in robust video cleanup for {video_path}: {e}")
        return False


def cleanup_video_file_threaded(video_path: Path, delay_before_cleanup: float = 5.0):
    """
    Clean up video file in a separate thread with robust error handling

    Args:
        video_path: Path to the video file to clean up
        delay_before_cleanup: Delay in seconds before attempting cleanup
    """
    import threading

    def delayed_cleanup():
        try:
            cleanup_video_file_robust(video_path, delay_before_cleanup)
        except Exception as e:
            logger.error(f"Error in threaded video cleanup for {video_path}: {e}")

    cleanup_thread = threading.Thread(target=delayed_cleanup, name=f"cleanup-{video_path.name}")
    cleanup_thread.daemon = True
    cleanup_thread.start()

    logger.debug(f"Started cleanup thread for {video_path.name}")


def cleanup_multiple_files(file_paths: List[Path], delay_between_files: float = 0.5) -> int:
    """
    Clean up multiple files with robust error handling

    Args:
        file_paths: List of file paths to clean up
        delay_between_files: Delay between cleaning each file

    Returns:
        Number of files successfully cleaned up
    """
    cleaned_count = 0

    for file_path in file_paths:
        try:
            if safe_delete_file(file_path):
                cleaned_count += 1

            if delay_between_files > 0:
                time.sleep(delay_between_files)

        except Exception as e:
            logger.error(f"Error cleaning up file {file_path}: {e}")

    return cleaned_count
