#!/usr/bin/env python3
"""
Installation script for LinkInsta dependencies
Handles common installation issues on Windows
"""
import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"   Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"   ✅ Success!")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Failed: {e}")
        if e.stdout:
            print(f"   Stdout: {e.stdout}")
        if e.stderr:
            print(f"   Stderr: {e.stderr}")
        return False

def install_dependencies():
    """Install dependencies step by step"""
    print("🚀 LinkInsta Dependency Installation")
    print("=" * 60)
    
    # Step 1: Upgrade pip and install build tools
    print("\n📦 Step 1: Installing build tools...")
    commands = [
        ("python -m pip install --upgrade pip", "Upgrading pip"),
        ("pip install setuptools>=60.0.0", "Installing setuptools"),
        ("pip install wheel>=0.37.0", "Installing wheel"),
    ]
    
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            print(f"⚠️  Warning: {desc} failed, continuing...")
    
    # Step 2: Install core dependencies one by one
    print("\n📦 Step 2: Installing core dependencies...")
    core_deps = [
        "requests>=2.25.0",
        "python-dotenv>=0.19.0", 
        "Pillow>=9.0.0",
        "emoji>=2.0.0",
        "schedule>=1.1.0",
        "colorlog>=6.0.0",
        "tqdm>=4.60.0"
    ]
    
    for dep in core_deps:
        run_command(f"pip install \"{dep}\"", f"Installing {dep}")
    
    # Step 3: Install text processing dependencies
    print("\n📦 Step 3: Installing text processing...")
    text_deps = [
        "arabic-reshaper>=3.0.0",
        "python-bidi>=0.4.0"
    ]
    
    for dep in text_deps:
        run_command(f"pip install \"{dep}\"", f"Installing {dep}")
    
    # Step 4: Install numpy (often problematic)
    print("\n📦 Step 4: Installing numpy...")
    numpy_commands = [
        ("pip install numpy>=1.21.0", "Installing numpy (latest)"),
        ("pip install numpy==1.24.3", "Installing specific numpy version"),
        ("pip install --only-binary=all numpy", "Installing numpy (binary only)")
    ]
    
    numpy_installed = False
    for cmd, desc in numpy_commands:
        if run_command(cmd, desc):
            numpy_installed = True
            break
    
    if not numpy_installed:
        print("⚠️  Warning: numpy installation failed, some features may not work")
    
    # Step 5: Install video processing dependencies
    print("\n📦 Step 5: Installing video processing...")
    video_deps = [
        "imageio>=2.20.0",
        "imageio-ffmpeg>=0.4.0",
        "opencv-python>=4.5.0"
    ]
    
    for dep in video_deps:
        if not run_command(f"pip install \"{dep}\"", f"Installing {dep}"):
            print(f"⚠️  Warning: {dep} failed, trying alternative...")
            # Try without version constraints
            base_name = dep.split(">=")[0]
            run_command(f"pip install {base_name}", f"Installing {base_name} (any version)")
    
    # Step 6: Install moviepy (often problematic)
    print("\n📦 Step 6: Installing moviepy...")
    moviepy_commands = [
        ("pip install moviepy>=1.0.0", "Installing moviepy (latest)"),
        ("pip install moviepy==1.0.3", "Installing specific moviepy version"),
        ("pip install --no-deps moviepy", "Installing moviepy (no deps)")
    ]
    
    for cmd, desc in moviepy_commands:
        if run_command(cmd, desc):
            break
    
    # Step 7: Install Telegram and Instagram APIs
    print("\n📦 Step 7: Installing API clients...")
    api_deps = [
        "python-telegram-bot>=20.0",
        "instagrapi>=1.16.0"
    ]
    
    for dep in api_deps:
        run_command(f"pip install \"{dep}\"", f"Installing {dep}")
    
    # Step 8: Install testing dependencies (optional)
    print("\n📦 Step 8: Installing testing dependencies (optional)...")
    test_deps = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.20.0"
    ]
    
    for dep in test_deps:
        if not run_command(f"pip install \"{dep}\"", f"Installing {dep}"):
            print(f"⚠️  Skipping {dep} (optional)")

def check_installation():
    """Check if key dependencies are installed"""
    print("\n🔍 Checking Installation...")
    
    key_modules = [
        "telegram",
        "instagrapi", 
        "PIL",
        "cv2",
        "requests",
        "dotenv",
        "emoji",
        "arabic_reshaper",
        "bidi"
    ]
    
    installed = []
    missing = []
    
    for module in key_modules:
        try:
            __import__(module)
            installed.append(module)
            print(f"   ✅ {module}")
        except ImportError:
            missing.append(module)
            print(f"   ❌ {module}")
    
    print(f"\n📊 Installation Summary:")
    print(f"   ✅ Installed: {len(installed)}/{len(key_modules)}")
    print(f"   ❌ Missing: {len(missing)}")
    
    if missing:
        print(f"\n⚠️  Missing modules: {', '.join(missing)}")
        print(f"   You may need to install these manually")
    else:
        print(f"\n🎉 All key dependencies installed successfully!")
    
    return len(missing) == 0

def main():
    """Main installation function"""
    print("🎬 LinkInsta - Dependency Installation Script")
    print("=" * 70)
    
    # Check Python version
    python_version = sys.version_info
    print(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        return False
    
    # Check if in virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    print(f"🔧 Virtual environment: {'Yes' if in_venv else 'No'}")
    
    if not in_venv:
        print("⚠️  Warning: Not in a virtual environment. Consider using one.")
    
    # Install dependencies
    install_dependencies()
    
    # Check installation
    success = check_installation()
    
    print(f"\n" + "=" * 70)
    if success:
        print("🎉 Installation completed successfully!")
        print("\n🚀 Next steps:")
        print("   1. Copy .env.example to .env")
        print("   2. Configure your API keys in .env")
        print("   3. Run: python generate_from_channel.py")
    else:
        print("⚠️  Installation completed with some issues")
        print("   Some features may not work properly")
        print("   Check the missing modules above")
    
    return success

if __name__ == "__main__":
    main()
