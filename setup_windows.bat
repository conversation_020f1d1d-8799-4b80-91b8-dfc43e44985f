@echo off
echo Setting up LinkInsta automation app on Windows Server...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Create virtual environment
echo Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install build tools first
echo Installing build tools...
pip install setuptools>=60.0.0 wheel>=0.37.0

REM Run custom installation script
echo Running dependency installation...
python install_dependencies.py
if errorlevel 1 (
    echo WARNING: Some dependencies may have failed to install
    echo You can try running: python install_dependencies.py
    echo Or install manually: pip install -r requirements.txt
)

REM Create necessary directories
echo Creating directories...
mkdir audio 2>nul
mkdir fonts 2>nul
mkdir output_videos 2>nul
mkdir temp 2>nul
mkdir logs 2>nul

REM Copy environment file
if not exist .env (
    echo Creating .env file from template...
    copy .env.example .env
    echo.
    echo IMPORTANT: Please edit .env file with your actual credentials:
    echo - TELEGRAM_BOT_TOKEN
    echo - INSTAGRAM_USERNAME
    echo - INSTAGRAM_PASSWORD
    echo.
)

REM Download Vazir font
echo Downloading Vazir font...
powershell -Command "& {
    $url = 'https://github.com/rastikerdar/vazir-font/releases/download/v30.1.0/vazir-font-v30.1.0.zip'
    $output = 'vazir-font.zip'
    try {
        Invoke-WebRequest -Uri $url -OutFile $output
        Expand-Archive -Path $output -DestinationPath 'fonts' -Force
        Remove-Item $output
        Write-Host 'Vazir font downloaded successfully'
    } catch {
        Write-Host 'Failed to download Vazir font. Please download manually from:'
        Write-Host 'https://github.com/rastikerdar/vazir-font/releases'
    }
}"

echo.
echo Setup completed successfully!
echo.
echo Next steps:
echo 1. Edit .env file with your credentials
echo 2. Run: venv\Scripts\activate.bat
echo 3. Run: python main.py
echo.
pause
