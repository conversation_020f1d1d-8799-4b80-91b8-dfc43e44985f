# 🔧 LinkInsta Installation Guide

This guide helps you install LinkInsta and resolve common installation issues.

## 🚀 Quick Installation

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone https://github.com/daadbina/linkyinsta.git
cd linkyinsta

# Run automated setup (Windows)
setup_windows.bat

# Or run Python installation script
python install_dependencies.py
```

### Option 2: Manual Installation

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate.bat
# Linux/Mac:
source venv/bin/activate

# Upgrade pip and install build tools
python -m pip install --upgrade pip
pip install setuptools>=60.0.0 wheel>=0.37.0

# Install dependencies step by step
pip install requests python-dotenv Pillow emoji
pip install arabic-reshaper python-bidi
pip install python-telegram-bot instagrapi
pip install numpy opencv-python imageio imageio-ffmpeg
pip install moviepy schedule colorlog tqdm
```

## 🔍 Common Issues & Solutions

### Issue 1: `Cannot import 'setuptools.build_meta'`

**Solution:**
```bash
# Upgrade setuptools and pip
python -m pip install --upgrade pip setuptools wheel

# Try installing with --no-build-isolation
pip install --no-build-isolation numpy
```

### Issue 2: `numpy` Installation Fails

**Solutions:**
```bash
# Option 1: Use precompiled wheel
pip install --only-binary=all numpy

# Option 2: Install specific version
pip install numpy==1.24.3

# Option 3: Use conda (if available)
conda install numpy
```

### Issue 3: `moviepy` Installation Issues

**Solutions:**
```bash
# Option 1: Install without dependencies first
pip install --no-deps moviepy
pip install imageio imageio-ffmpeg

# Option 2: Use older version
pip install moviepy==1.0.3

# Option 3: Install dependencies separately
pip install decorator imageio imageio-ffmpeg tqdm requests
pip install moviepy
```

### Issue 4: `opencv-python` Installation Fails

**Solutions:**
```bash
# Option 1: Install headless version
pip install opencv-python-headless

# Option 2: Use specific version
pip install opencv-python==********

# Option 3: Install from conda-forge
conda install -c conda-forge opencv
```

### Issue 5: Virtual Environment Issues

**Solutions:**
```bash
# Delete and recreate virtual environment
rmdir /s venv  # Windows
rm -rf venv    # Linux/Mac

python -m venv venv
# Activate and try again
```

## 🐍 Python Version Requirements

- **Minimum:** Python 3.8
- **Recommended:** Python 3.9 or 3.10
- **Tested:** Python 3.8, 3.9, 3.10, 3.11

Check your Python version:
```bash
python --version
```

## 🔧 System Requirements

### Windows
- Windows 10 or later
- Visual Studio Build Tools (for some packages)
- At least 2GB free disk space

### Linux
- Ubuntu 18.04+ or equivalent
- Build essentials: `sudo apt install build-essential`
- Python development headers: `sudo apt install python3-dev`

### macOS
- macOS 10.14 or later
- Xcode Command Line Tools: `xcode-select --install`

## 📦 Dependency Overview

### Core Dependencies
- `python-telegram-bot` - Telegram Bot API
- `instagrapi` - Instagram API
- `Pillow` - Image processing
- `requests` - HTTP requests
- `python-dotenv` - Environment variables

### Video Processing
- `opencv-python` - Computer vision
- `moviepy` - Video editing
- `imageio` - Image/video I/O
- `numpy` - Numerical computing

### Text Processing
- `arabic-reshaper` - Arabic text shaping
- `python-bidi` - Bidirectional text
- `emoji` - Emoji handling

## 🧪 Testing Installation

After installation, test if everything works:

```bash
# Test core imports
python -c "import telegram, instagrapi, PIL, cv2, requests"

# Test text processing
python -c "import arabic_reshaper, bidi, emoji"

# Test video processing
python -c "import moviepy, imageio, numpy"

# Run installation check
python install_dependencies.py
```

## 🔄 Alternative Installation Methods

### Using Conda

```bash
# Create conda environment
conda create -n linkinsta python=3.9
conda activate linkinsta

# Install available packages from conda-forge
conda install -c conda-forge numpy opencv pillow requests
conda install -c conda-forge imageio imageio-ffmpeg

# Install remaining with pip
pip install python-telegram-bot instagrapi python-dotenv
pip install arabic-reshaper python-bidi emoji moviepy
```

### Using Poetry

```bash
# Install poetry if not available
pip install poetry

# Install dependencies
poetry install

# Activate environment
poetry shell
```

## 🆘 Getting Help

If you're still having issues:

1. **Check Python version:** Ensure you're using Python 3.8+
2. **Update pip:** `python -m pip install --upgrade pip`
3. **Clear pip cache:** `pip cache purge`
4. **Try different Python:** Use different Python installation
5. **Check logs:** Look for specific error messages
6. **Manual install:** Install each package individually

### Common Error Messages

**"Microsoft Visual C++ 14.0 is required"**
- Install Visual Studio Build Tools
- Or use precompiled wheels: `pip install --only-binary=all package_name`

**"Failed building wheel for package"**
- Install build tools: `pip install setuptools wheel`
- Try: `pip install --no-build-isolation package_name`

**"No module named '_ctypes'"**
- Reinstall Python with all optional components
- Or use conda instead of pip

## ✅ Verification

Once installed successfully, you should be able to:

```bash
# Generate videos from channel
python generate_from_channel.py

# Run main application
python main.py

# Check all systems
python -c "
import sys
print(f'Python: {sys.version}')

try:
    import telegram, instagrapi, PIL, cv2
    print('✅ Core dependencies: OK')
except ImportError as e:
    print(f'❌ Core dependencies: {e}')

try:
    import arabic_reshaper, bidi, emoji
    print('✅ Text processing: OK')
except ImportError as e:
    print(f'❌ Text processing: {e}')

try:
    import moviepy, imageio, numpy
    print('✅ Video processing: OK')
except ImportError as e:
    print(f'❌ Video processing: {e}')
"
```

---

**🎯 Need more help?** Create an issue on GitHub with:
- Your Python version
- Operating system
- Full error message
- Steps you've tried
