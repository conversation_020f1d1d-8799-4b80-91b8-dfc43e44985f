#!/usr/bin/env python3
"""
Test script to verify float-to-integer conversion fixes in video generators
"""
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_text_video_generation():
    """Test text-only video generation for float conversion issues"""
    print("🎬 Testing Text Video Generation...")
    
    try:
        from src.video_generators.text_video_generator import TextVideoGenerator
        from src.config import Config
        
        # Create generator
        generator = TextVideoGenerator()
        
        # Test with various text lengths
        test_texts = [
            "Short test",
            "This is a medium length test message that should wrap to multiple lines",
            "This is a very long test message that contains multiple sentences and should definitely wrap to several lines to test the text positioning and coordinate calculations thoroughly."
        ]
        
        for i, text in enumerate(test_texts):
            print(f"  Testing text {i+1}: {text[:50]}...")
            
            try:
                # Generate video
                output_path = Path(f"test_output/test_text_{i+1}.mp4")
                output_path.parent.mkdir(exist_ok=True)
                
                result = generator.generate_video(text, output_path)
                
                if result:
                    print(f"    ✅ Generated successfully: {output_path}")
                else:
                    print(f"    ❌ Generation failed")
                    
            except Exception as e:
                if "float" in str(e).lower() and "integer" in str(e).lower():
                    print(f"    ❌ FLOAT ERROR: {e}")
                    return False
                else:
                    print(f"    ⚠️  Other error: {e}")
        
        print("  ✅ Text video generation tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Text video generation test failed: {e}")
        return False

def test_text_image_video_generation():
    """Test text+image video generation for float conversion issues"""
    print("🖼️ Testing Text+Image Video Generation...")
    
    try:
        from src.video_generators.text_image_video_generator import TextImageVideoGenerator
        
        # Create generator
        generator = TextImageVideoGenerator()
        
        # Test with sample text
        test_text = "Testing text and image combination with proper coordinate calculations"
        
        print(f"  Testing: {test_text[:50]}...")
        
        try:
            # Generate video (without actual images for testing)
            output_path = Path("test_output/test_text_image.mp4")
            output_path.parent.mkdir(exist_ok=True)
            
            result = generator.generate_video(test_text, [], output_path)
            
            if result:
                print(f"    ✅ Generated successfully: {output_path}")
            else:
                print(f"    ❌ Generation failed")
                
        except Exception as e:
            if "float" in str(e).lower() and "integer" in str(e).lower():
                print(f"    ❌ FLOAT ERROR: {e}")
                return False
            else:
                print(f"    ⚠️  Other error: {e}")
        
        print("  ✅ Text+Image video generation tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Text+Image video generation test failed: {e}")
        return False

def test_video_text_overlay():
    """Test video text overlay for float conversion issues"""
    print("📹 Testing Video Text Overlay...")
    
    try:
        from src.video_generators.video_text_generator import VideoTextGenerator
        
        # Create generator
        generator = VideoTextGenerator()
        
        # Test with sample text
        test_text = "Testing video overlay with proper coordinate calculations and positioning"
        
        print(f"  Testing overlay: {test_text[:50]}...")
        
        try:
            # Test the text overlay creation method directly
            from PIL import Image
            
            # Create a test video frame size
            video_width, video_height = 1080, 1920
            
            # Test the overlay creation
            overlay = generator._create_text_overlay_image(test_text, video_width, video_height)
            
            if overlay and overlay.size == (video_width, video_height):
                print(f"    ✅ Text overlay created successfully: {overlay.size}")
            else:
                print(f"    ❌ Text overlay creation failed")
                return False
                
        except Exception as e:
            if "float" in str(e).lower() and "integer" in str(e).lower():
                print(f"    ❌ FLOAT ERROR: {e}")
                return False
            else:
                print(f"    ⚠️  Other error: {e}")
        
        print("  ✅ Video text overlay tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Video text overlay test failed: {e}")
        return False

def test_coordinate_calculations():
    """Test coordinate calculation functions directly"""
    print("📐 Testing Coordinate Calculations...")
    
    try:
        # Test various coordinate calculations that might produce floats
        test_cases = [
            {"width": 1080, "height": 1920, "factor": 0.25},
            {"width": 1080, "height": 1920, "factor": 0.33},
            {"width": 1080, "height": 1920, "factor": 0.5},
            {"width": 720, "height": 1280, "factor": 0.75},
        ]
        
        for case in test_cases:
            width = case["width"]
            height = case["height"]
            factor = case["factor"]
            
            # Test calculations that were causing issues
            start_y = int(height * factor)  # This was the main issue
            bg_x = int((width - 800) // 2)
            bg_y = int(start_y - 30)
            
            print(f"    Width: {width}, Height: {height}, Factor: {factor}")
            print(f"      start_y: {start_y} (type: {type(start_y)})")
            print(f"      bg_x: {bg_x} (type: {type(bg_x)})")
            print(f"      bg_y: {bg_y} (type: {type(bg_y)})")
            
            # Verify all are integers
            if not all(isinstance(val, int) for val in [start_y, bg_x, bg_y]):
                print(f"    ❌ Non-integer coordinates found!")
                return False
        
        print("  ✅ Coordinate calculation tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Coordinate calculation test failed: {e}")
        return False

def main():
    """Run all float conversion tests"""
    print("🔧 Testing Float-to-Integer Conversion Fixes")
    print("=" * 50)
    
    # Create test output directory
    Path("test_output").mkdir(exist_ok=True)
    
    tests = [
        test_coordinate_calculations,
        test_text_video_generation,
        test_text_image_video_generation,
        test_video_text_overlay,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All float conversion fixes are working correctly!")
        return True
    else:
        print("⚠️  Some tests failed - float conversion issues may still exist")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ Float conversion fixes verified successfully!")
        else:
            print("\n❌ Float conversion issues detected!")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback
        traceback.print_exc()
