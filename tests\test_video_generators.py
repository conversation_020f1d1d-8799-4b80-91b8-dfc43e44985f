"""
Tests for video generator functionality
"""
import pytest
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch
from PIL import Image

from src.video_generators import TextVideoGenerator, TextImageVideoGenerator, VideoTextGenerator
from src.config import Config

class TestVideoGenerators:
    """Test cases for video generators"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_config(self, temp_dir):
        """Mock configuration for tests"""
        with patch.object(Config, 'BASE_DIR', temp_dir):
            with patch.object(Config, 'TEMP_DIR', temp_dir / 'temp'):
                with patch.object(Config, 'AUDIO_DIR', temp_dir / 'audio'):
                    Config.TEMP_DIR.mkdir(exist_ok=True)
                    Config.AUDIO_DIR.mkdir(exist_ok=True)
                    yield Config
    
    @pytest.fixture
    def text_generator(self, mock_config):
        """Create TextVideoGenerator instance"""
        return TextVideoGenerator()
    
    @pytest.fixture
    def text_image_generator(self, mock_config):
        """Create TextImageVideoGenerator instance"""
        return TextImageVideoGenerator()
    
    @pytest.fixture
    def video_text_generator(self, mock_config):
        """Create VideoTextGenerator instance"""
        return VideoTextGenerator()
    
    def test_farsi_text_preparation(self, text_generator):
        """Test Farsi text preparation"""
        farsi_text = "سلام دنیا"
        prepared_text = text_generator._prepare_farsi_text(farsi_text)
        
        # Should not raise an exception and return a string
        assert isinstance(prepared_text, str)
        assert len(prepared_text) > 0
    
    def test_text_wrapping(self, text_generator):
        """Test text wrapping functionality"""
        long_text = "This is a very long text that should be wrapped into multiple lines for proper display"
        
        # Mock font bbox method
        text_generator.font.getbbox = Mock(return_value=(0, 0, 100, 20))
        
        lines = text_generator._wrap_text(long_text, 200)
        
        assert isinstance(lines, list)
        assert len(lines) > 1  # Should be wrapped into multiple lines
    
    def test_twitter_frame_creation(self, text_generator):
        """Test Twitter-like frame creation"""
        test_text = "Test message for frame creation"
        
        # Mock font methods
        text_generator.font.getbbox = Mock(return_value=(0, 0, 100, 20))
        text_generator.small_font.getbbox = Mock(return_value=(0, 0, 50, 15))
        
        frame = text_generator._create_twitter_like_frame(test_text)
        
        assert isinstance(frame, Image.Image)
        assert frame.size == (text_generator.width, text_generator.height)
    
    def test_random_background_music(self, text_generator, temp_dir):
        """Test random background music selection"""
        # Create mock audio files
        audio_dir = temp_dir / 'audio'
        audio_dir.mkdir(exist_ok=True)
        
        (audio_dir / 'track1.mp3').write_text("fake audio")
        (audio_dir / 'track2.mp3').write_text("fake audio")
        
        with patch.object(Config, 'AUDIO_DIR', audio_dir):
            music_file = text_generator._get_random_background_music()
            
            assert music_file is not None
            assert music_file.exists()
            assert music_file.suffix == '.mp3'
    
    @patch('src.video_generators.text_video_generator.ImageClip')
    @patch('src.video_generators.text_video_generator.AudioFileClip')
    def test_text_video_generation(self, mock_audio_clip, mock_image_clip, text_generator, temp_dir):
        """Test text video generation"""
        # Mock moviepy components
        mock_video_clip = Mock()
        mock_image_clip.return_value = mock_video_clip
        mock_video_clip.set_duration.return_value = mock_video_clip
        mock_video_clip.set_audio.return_value = mock_video_clip
        mock_video_clip.write_videofile = Mock()
        mock_video_clip.close = Mock()
        
        mock_audio = Mock()
        mock_audio_clip.return_value = mock_audio
        mock_audio.duration = 30
        mock_audio.subclip.return_value = mock_audio
        mock_audio.volumex.return_value = mock_audio
        mock_audio.close = Mock()
        
        # Create audio file
        audio_file = temp_dir / 'audio' / 'test.mp3'
        audio_file.parent.mkdir(exist_ok=True)
        audio_file.write_text("fake audio")
        
        output_path = temp_dir / 'output.mp4'
        
        # Mock font methods
        text_generator.font.getbbox = Mock(return_value=(0, 0, 100, 20))
        text_generator.small_font.getbbox = Mock(return_value=(0, 0, 50, 15))
        
        result = text_generator.generate_video("Test text", output_path)
        
        assert result is True
        mock_video_clip.write_videofile.assert_called_once()
    
    def test_image_resizing(self, text_image_generator):
        """Test image resizing functionality"""
        # Create test image
        test_image = Image.new('RGB', (800, 600), color='red')
        
        resized = text_image_generator._resize_image_to_fit(test_image, 400, 300)
        
        assert resized.size[0] <= 400
        assert resized.size[1] <= 300
        # Should maintain aspect ratio
        original_ratio = 800 / 600
        new_ratio = resized.size[0] / resized.size[1]
        assert abs(original_ratio - new_ratio) < 0.01
    
    def test_image_collage_creation(self, text_image_generator):
        """Test image collage creation"""
        # Create test images
        images = [
            Image.new('RGB', (100, 100), color='red'),
            Image.new('RGB', (100, 100), color='green'),
            Image.new('RGB', (100, 100), color='blue')
        ]
        
        collage = text_image_generator._create_image_collage(images, 400, 300)
        
        assert isinstance(collage, Image.Image)
        assert collage.size == (400, 300)
    
    def test_video_aspect_ratio_conversion(self, video_text_generator):
        """Test video aspect ratio conversion"""
        # This would require actual video files and moviepy
        # For now, test the logic without actual video processing
        
        # Test aspect ratio calculation
        target_ratio = video_text_generator.target_aspect_ratio
        expected_ratio = video_text_generator.height / video_text_generator.width
        
        assert target_ratio == expected_ratio
    
    def test_text_overlay_creation(self, video_text_generator):
        """Test text overlay creation for videos"""
        test_text = "Test overlay text"
        duration = 10.0
        
        # Mock TextClip
        with patch('src.video_generators.video_text_generator.TextClip') as mock_text_clip:
            mock_clip = Mock()
            mock_text_clip.return_value = mock_clip
            mock_clip.set_duration.return_value = mock_clip
            mock_clip.set_position.return_value = mock_clip
            mock_clip.h = 50  # Mock height
            
            overlay = video_text_generator._create_text_overlay(test_text, duration)
            
            assert overlay is not None
            mock_text_clip.assert_called_once()
            mock_clip.set_duration.assert_called_with(duration)
    
    def test_video_trimming_logic(self, video_text_generator):
        """Test video trimming logic"""
        # Mock video clip
        mock_video = Mock()
        mock_video.duration = 30.0  # 30 seconds
        mock_video.subclip.return_value = mock_video
        
        target_duration = 10.0
        
        trimmed = video_text_generator._trim_video_to_duration(mock_video, target_duration)
        
        # Should call subclip for trimming
        mock_video.subclip.assert_called_once()
        
        # Test case where video is shorter than target
        mock_video.duration = 5.0
        mock_video.subclip.reset_mock()
        
        result = video_text_generator._trim_video_to_duration(mock_video, target_duration)
        
        # Should return original video without trimming
        assert result == mock_video
        mock_video.subclip.assert_not_called()

if __name__ == '__main__':
    pytest.main([__file__])
