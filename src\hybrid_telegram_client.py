"""
Hybrid Telegram Client: Bot API + User API for reaction access
"""
import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import pytz

from src.config import Config
from src.telegram_client import TelegramClient, TelegramMessage

logger = logging.getLogger(__name__)

class HybridTelegramClient:
    """
    Hybrid client that combines Bot API and User API:
    - Bot API: For message retrieval (prevents duplication)
    - User API: For reaction counting (can access real reactions)
    """
    
    def __init__(self):
        self.bot_client = TelegramClient()
        self.user_client = None
        self.user_client_initialized = False
        
    async def initialize(self) -> bool:
        """Initialize both Bot API and User API clients"""
        try:
            # Always initialize Bot API
            bot_success = await self.bot_client.initialize()
            if not bot_success:
                logger.error("Failed to initialize Bot API client")
                return False
            
            logger.info("✅ Bot API client initialized")
            
            # Try to initialize User API if credentials available
            if Config.USE_TELEGRAM_USER_API and Config.TELEGRAM_API_ID and Config.TELEGRAM_API_HASH:
                try:
                    await self._initialize_user_client()
                    logger.info("✅ User API client initialized for reaction access")
                except Exception as e:
                    logger.warning(f"User API initialization failed: {e}")
                    logger.info("Continuing with Bot API only (no reaction data)")
            else:
                logger.info("User API credentials not configured - using Bot API only")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize hybrid client: {e}")
            return False
    
    async def _initialize_user_client(self):
        """Initialize User API client for reaction access"""
        try:
            from telethon import TelegramClient as TelethonClient
            
            # Create User API client
            self.user_client = TelethonClient(
                'reaction_session',
                Config.TELEGRAM_API_ID,
                Config.TELEGRAM_API_HASH
            )
            
            # Connect and authenticate
            await self.user_client.start(phone=Config.TELEGRAM_PHONE)
            self.user_client_initialized = True
            
        except ImportError:
            logger.error("telethon not installed - cannot use User API")
            raise
        except Exception as e:
            logger.error(f"User API initialization failed: {e}")
            raise
    
    async def get_recent_messages(self, limit: int = 10, for_polling: bool = False) -> List[TelegramMessage]:
        """
        Get recent messages using Bot API, then enhance with User API reaction data
        """
        try:
            # Step 1: Get messages using Bot API (prevents duplication)
            logger.info("🔄 Getting messages via Bot API...")
            bot_messages = await self.bot_client.get_recent_messages(limit=limit, for_polling=for_polling)
            
            if not bot_messages:
                logger.info("No messages from Bot API")
                return []
            
            logger.info(f"📱 Bot API retrieved {len(bot_messages)} messages")
            
            # Step 2: Enhance with User API reaction data if available
            if self.user_client_initialized and self.user_client:
                logger.info("🔥 Enhancing with User API reaction data...")
                enhanced_messages = await self._enhance_with_reactions(bot_messages)
                return enhanced_messages
            else:
                logger.info("User API not available - using Bot API only")
                return bot_messages
                
        except Exception as e:
            logger.error(f"Error in hybrid message retrieval: {e}")
            return []
    
    async def _enhance_with_reactions(self, bot_messages: List[TelegramMessage]) -> List[TelegramMessage]:
        """Enhance Bot API messages with User API reaction data"""
        enhanced_messages = []
        
        try:
            # Get the channel entity
            channel_entity = await self.user_client.get_entity(Config.TELEGRAM_CHAT_ID)
            
            for bot_msg in bot_messages:
                try:
                    # Get the same message via User API to access reactions
                    user_msg = await self.user_client.get_messages(
                        channel_entity,
                        ids=bot_msg.message_id
                    )

                    # Handle both single message and list responses
                    if user_msg:
                        if isinstance(user_msg, list) and len(user_msg) > 0:
                            user_message = user_msg[0]
                        else:
                            user_message = user_msg
                        
                        # Count real reactions
                        real_reaction_count = 0
                        if hasattr(user_message, 'reactions') and user_message.reactions:
                            if hasattr(user_message.reactions, 'results'):
                                for reaction in user_message.reactions.results:
                                    if hasattr(reaction, 'count'):
                                        real_reaction_count += reaction.count
                        
                        # Update the Bot API message with real reaction count
                        bot_msg.reaction_count = real_reaction_count
                        
                        logger.debug(f"Message {bot_msg.message_id}: {real_reaction_count} real reactions")
                    else:
                        logger.warning(f"Could not get User API data for message {bot_msg.message_id}")
                        
                except Exception as e:
                    logger.warning(f"Failed to get reactions for message {bot_msg.message_id}: {e}")
                
                enhanced_messages.append(bot_msg)
            
            # Log reaction summary
            total_reactions = sum(msg.reaction_count for msg in enhanced_messages)
            messages_with_reactions = len([msg for msg in enhanced_messages if msg.reaction_count > 0])
            
            logger.info(f"🔥 Reaction Enhancement Results:")
            logger.info(f"   Total messages: {len(enhanced_messages)}")
            logger.info(f"   Messages with reactions: {messages_with_reactions}")
            logger.info(f"   Total reactions: {total_reactions}")
            
            return enhanced_messages
            
        except Exception as e:
            logger.error(f"Error enhancing messages with reactions: {e}")
            return bot_messages  # Return original messages if enhancement fails
    
    async def close(self):
        """Close both clients"""
        try:
            if self.bot_client:
                await self.bot_client.close()
            
            if self.user_client and self.user_client_initialized:
                await self.user_client.disconnect()
                
        except Exception as e:
            logger.warning(f"Error closing hybrid client: {e}")
    
    # Delegate other methods to bot_client
    async def download_media(self, media_file: Dict[str, Any], download_path) -> Optional:
        """Delegate media download to bot client"""
        return await self.bot_client.download_media(media_file, download_path)
    
    def mark_message_processed(self, message_id: int):
        """Delegate to bot client"""
        return self.bot_client.mark_message_processed(message_id)
