#!/usr/bin/env python3
"""
Test to find message #22949 with 2 reactions specifically
"""
import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_specific_message_reactions():
    """Test reaction detection for specific message #22949"""
    print("🎯 Testing Specific Message #22949 Reactions")
    print("=" * 60)
    
    try:
        from src.hybrid_telegram_client import HybridTelegramClient
        from src.config import Config
        
        # Create hybrid client
        hybrid_client = HybridTelegramClient()
        
        print("🔄 Initializing hybrid client...")
        if await hybrid_client.initialize():
            print("✅ Hybrid client initialized")
            
            # Test User API directly for message #22949
            if hybrid_client.user_client_initialized and hybrid_client.user_client:
                print("\n🔍 Testing User API directly for message #22949...")
                
                try:
                    # Get channel entity
                    channel_entity = await hybrid_client.user_client.get_entity(Config.TELEGRAM_CHAT_ID)
                    print(f"✅ Got channel entity: {channel_entity.title}")
                    
                    # Get specific message #22949
                    target_message_id = 22949
                    user_msg = await hybrid_client.user_client.get_messages(
                        channel_entity, 
                        ids=target_message_id
                    )
                    
                    if user_msg:
                        print(f"✅ Found message #{target_message_id}")
                        
                        # Handle response format
                        if isinstance(user_msg, list) and len(user_msg) > 0:
                            message = user_msg[0]
                        else:
                            message = user_msg
                        
                        print(f"📝 Content: {(message.text or 'No text')[:100]}...")
                        
                        # Check reactions
                        real_reaction_count = 0
                        if hasattr(message, 'reactions') and message.reactions:
                            print(f"✅ Message has reactions attribute!")
                            
                            if hasattr(message.reactions, 'results'):
                                print(f"✅ Reactions has results!")
                                for i, reaction in enumerate(message.reactions.results):
                                    if hasattr(reaction, 'count'):
                                        print(f"   Reaction {i+1}: {reaction.count} count")
                                        real_reaction_count += reaction.count
                                    if hasattr(reaction, 'reaction'):
                                        print(f"   Reaction type: {reaction.reaction}")
                            
                            if hasattr(message.reactions, 'total_count'):
                                print(f"✅ Total count: {message.reactions.total_count}")
                                real_reaction_count = message.reactions.total_count
                        else:
                            print(f"❌ No reactions attribute found")
                        
                        print(f"\n🔥 FINAL RESULT:")
                        print(f"   Message #{target_message_id}")
                        print(f"   Real reactions: {real_reaction_count}")
                        
                        if real_reaction_count == 2:
                            print(f"   🎯 PERFECT! Found the 2 reactions you mentioned!")
                        elif real_reaction_count > 0:
                            print(f"   ✅ Found {real_reaction_count} reactions")
                        else:
                            print(f"   📝 No reactions detected")
                        
                        return {
                            'message_id': target_message_id,
                            'reactions': real_reaction_count,
                            'content': message.text or 'No text'
                        }
                    else:
                        print(f"❌ Could not find message #{target_message_id}")
                        return None
                        
                except Exception as e:
                    print(f"❌ Error testing specific message: {e}")
                    import traceback
                    traceback.print_exc()
                    return None
            else:
                print("❌ User API not initialized")
                return None
        else:
            print("❌ Failed to initialize hybrid client")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        try:
            await hybrid_client.close()
        except:
            pass

async def test_range_of_messages():
    """Test a range of messages to find ones with reactions"""
    print("\n🔍 Testing Range of Messages for Reactions")
    print("=" * 60)
    
    try:
        from src.hybrid_telegram_client import HybridTelegramClient
        from src.config import Config
        
        # Create hybrid client
        hybrid_client = HybridTelegramClient()
        
        if await hybrid_client.initialize():
            if hybrid_client.user_client_initialized and hybrid_client.user_client:
                print("🔄 Scanning messages 22945-22952 for reactions...")
                
                try:
                    channel_entity = await hybrid_client.user_client.get_entity(Config.TELEGRAM_CHAT_ID)
                    
                    messages_with_reactions = []
                    
                    # Check range of messages
                    for msg_id in range(22945, 22953):
                        try:
                            user_msg = await hybrid_client.user_client.get_messages(
                                channel_entity, 
                                ids=msg_id
                            )
                            
                            if user_msg:
                                if isinstance(user_msg, list) and len(user_msg) > 0:
                                    message = user_msg[0]
                                else:
                                    message = user_msg
                                
                                # Count reactions
                                reaction_count = 0
                                if hasattr(message, 'reactions') and message.reactions:
                                    if hasattr(message.reactions, 'total_count'):
                                        reaction_count = message.reactions.total_count
                                    elif hasattr(message.reactions, 'results'):
                                        for reaction in message.reactions.results:
                                            if hasattr(reaction, 'count'):
                                                reaction_count += reaction.count
                                
                                if reaction_count > 0:
                                    messages_with_reactions.append({
                                        'id': msg_id,
                                        'reactions': reaction_count,
                                        'content': (message.text or 'No text')[:60]
                                    })
                                    print(f"   ✅ Message #{msg_id}: {reaction_count} reactions")
                                else:
                                    print(f"   📝 Message #{msg_id}: 0 reactions")
                        
                        except Exception as e:
                            print(f"   ❌ Message #{msg_id}: Error - {e}")
                    
                    print(f"\n📊 SCAN RESULTS:")
                    print(f"   Messages with reactions: {len(messages_with_reactions)}")
                    
                    if messages_with_reactions:
                        print(f"   🔥 FOUND MESSAGES WITH REACTIONS:")
                        for msg in messages_with_reactions:
                            print(f"      #{msg['id']}: {msg['reactions']} reactions - {msg['content']}...")
                        
                        # Find the one with most reactions
                        best = max(messages_with_reactions, key=lambda x: x['reactions'])
                        print(f"\n   🏆 BEST MESSAGE:")
                        print(f"      #{best['id']} with {best['reactions']} reactions")
                        
                        return best
                    else:
                        print(f"   📝 No messages with reactions found in this range")
                        return None
                        
                except Exception as e:
                    print(f"❌ Error scanning messages: {e}")
                    return None
            else:
                print("❌ User API not available")
                return None
        else:
            print("❌ Failed to initialize")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        try:
            await hybrid_client.close()
        except:
            pass

async def main():
    """Main test function"""
    print("🚀 Find Message with Reactions Test")
    print("=" * 70)
    
    print("🎯 GOALS:")
    print("1. Find message #22949 with 2 reactions")
    print("2. Test User API reaction detection")
    print("3. Scan for other messages with reactions")
    print()
    
    # Test 1: Specific message #22949
    specific_result = await test_specific_message_reactions()
    
    # Test 2: Range scan
    range_result = await test_range_of_messages()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 FINAL RESULTS")
    print("=" * 70)
    
    if specific_result:
        print(f"✅ Message #22949 test:")
        print(f"   Reactions: {specific_result['reactions']}")
        if specific_result['reactions'] == 2:
            print(f"   🎯 PERFECT! Found your 2 reactions!")
        elif specific_result['reactions'] > 0:
            print(f"   ✅ Found reactions, but not 2")
        else:
            print(f"   📝 No reactions found")
    
    if range_result:
        print(f"\n✅ Best message in range:")
        print(f"   #{range_result['id']} with {range_result['reactions']} reactions")
    
    print(f"\n💡 NEXT STEPS:")
    if specific_result and specific_result['reactions'] > 0:
        print(f"🎉 User API reaction detection WORKING!")
        print(f"✅ Can now implement proper reaction-based selection")
    else:
        print(f"🔧 Need to debug User API reaction access")

if __name__ == "__main__":
    asyncio.run(main())
