#!/usr/bin/env python3
"""
Test script for YouTube integration
"""
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import Config
from src.youtube_client import YouTubeClient
from src.instagram_client import InstagramClient
from src.utils.logger import logger

def test_configuration():
    """Test if configuration is properly loaded"""
    print("🔧 Testing Configuration...")
    
    print(f"✅ YouTube Client Secrets File: {Config.YOUTUBE_CLIENT_SECRETS_FILE}")
    print(f"✅ YouTube Credentials File: {Config.YOUTUBE_CREDENTIALS_FILE}")
    print(f"✅ YouTube Posting Enabled: {Config.ENABLE_YOUTUBE_POSTING}")
    print(f"✅ Instagram Posting Enabled: {Config.ENABLE_INSTAGRAM_POSTING}")
    print(f"✅ Test Mode: {Config.TEST_MODE}")
    
    # Check if OA<PERSON> file exists
    if Config.YOUTUBE_CLIENT_SECRETS_FILE.exists():
        print(f"✅ OAuth file found: {Config.YOUTUBE_CLIENT_SECRETS_FILE}")
    else:
        print(f"❌ OAuth file NOT found: {Config.YOUTUBE_CLIENT_SECRETS_FILE}")
        return False
    
    return True

def test_youtube_client():
    """Test YouTube client initialization"""
    print("\n📺 Testing YouTube Client...")
    
    try:
        youtube_client = YouTubeClient()
        print("✅ YouTube client created successfully")
        
        # Test authentication (this will open browser if needed)
        print("🔐 Testing YouTube authentication...")
        if youtube_client.authenticate():
            print("✅ YouTube authentication successful!")
            
            # Get channel info
            channel_info = youtube_client.get_channel_info()
            if channel_info:
                print(f"✅ Channel: {channel_info['title']}")
                print(f"✅ Subscribers: {channel_info['subscriber_count']}")
                print(f"✅ Videos: {channel_info['video_count']}")
            
            return True
        else:
            print("❌ YouTube authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ YouTube client error: {e}")
        return False

def test_instagram_caption():
    """Test Instagram caption with Farsi text"""
    print("\n📱 Testing Instagram Caption...")
    
    try:
        instagram_client = InstagramClient()
        
        # Test caption preparation
        test_text = "This is a test message for our automation system"
        caption = instagram_client._prepare_caption(test_text)
        
        print("✅ Caption generated successfully:")
        print("=" * 50)
        print(caption)
        print("=" * 50)
        
        # Check if Farsi text is included
        if "این محتوا بر اساس توییت‌های شما ساخته شده است" in caption:
            print("✅ Farsi sentence found in caption!")
            return True
        else:
            print("❌ Farsi sentence NOT found in caption")
            return False
            
    except Exception as e:
        print(f"❌ Instagram caption test error: {e}")
        return False

def test_video_upload_simulation():
    """Test video upload simulation"""
    print("\n🎬 Testing Video Upload Simulation...")
    
    # Find a test video file
    output_dir = Path("output_videos")
    video_files = list(output_dir.glob("*.mp4"))
    
    if not video_files:
        print("❌ No video files found in output_videos directory")
        return False
    
    test_video = video_files[0]
    print(f"📹 Using test video: {test_video.name}")
    
    try:
        youtube_client = YouTubeClient()
        
        if not youtube_client.authenticate():
            print("❌ YouTube authentication failed")
            return False
        
        # Test upload (in simulation mode)
        print("🧪 Simulating video upload...")
        
        test_title = "Test Upload - LinkInsta Content"
        test_description = "This is a test upload from LinkInsta automation system"
        
        print(f"📝 Title: {test_title}")
        print(f"📝 Description: {test_description}")
        print(f"📝 Video: {test_video}")
        
        # Note: This would actually upload in real mode
        # For testing, we just validate the setup
        print("✅ Upload simulation successful (not actually uploading)")
        
        return True
        
    except Exception as e:
        print(f"❌ Video upload simulation error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 LinkInsta YouTube Integration Test")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Instagram Caption", test_instagram_caption),
        ("YouTube Client", test_youtube_client),
        ("Video Upload Simulation", test_video_upload_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Your YouTube integration is ready!")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
