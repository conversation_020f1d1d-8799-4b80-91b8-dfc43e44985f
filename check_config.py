#!/usr/bin/env python3
"""
Check current configuration and fix any issues
"""
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def check_env_file():
    """Check .env file contents"""
    print("📄 Checking .env file...")
    env_file = Path('.env')
    
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the specific setting
        for line_num, line in enumerate(content.split('\n'), 1):
            if 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE' in line:
                print(f"Line {line_num}: {line}")
                
                if '=false' in line:
                    print("✅ Setting is FALSE - should allow 24/7 posting")
                elif '=true' in line:
                    print("⚠️  Setting is TRUE - will respect posting hours")
                else:
                    print("❌ Setting format unclear")
    else:
        print("❌ .env file not found")

def check_config_loading():
    """Check how config is being loaded"""
    print("\n🔧 Checking config loading...")
    
    try:
        from src.config import Config
        
        print(f"RESPECT_POSTING_HOURS_IN_INSTANT_MODE: {Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE}")
        print(f"INSTANT_POSTING_MODE: {Config.INSTANT_POSTING_MODE}")
        print(f"POSTING_START_HOUR: {Config.POSTING_START_HOUR}")
        print(f"POSTING_END_HOUR: {Config.POSTING_END_HOUR}")
        
        if Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE:
            print("⚠️  Config shows posting hours ARE being respected")
        else:
            print("✅ Config shows posting hours are NOT being respected")
            
    except Exception as e:
        print(f"❌ Error loading config: {e}")

def check_main_app_logic():
    """Check the main app logic"""
    print("\n🔍 Checking main app logic...")
    
    try:
        # Read the main app file
        main_app_file = Path('src/main_app.py')
        if main_app_file.exists():
            with open(main_app_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for the posting hours check
            if 'RESPECT_POSTING_HOURS_IN_INSTANT_MODE' in content:
                print("✅ Found posting hours check in main app")
                
                # Check if the logic is correct
                if 'if Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE:' in content:
                    print("✅ Logic looks correct - should respect config setting")
                else:
                    print("⚠️  Logic might be incorrect")
            else:
                print("❌ Posting hours check not found in main app")
        else:
            print("❌ main_app.py not found")
            
    except Exception as e:
        print(f"❌ Error checking main app: {e}")

def main():
    """Main function"""
    print("🔍 Configuration Diagnostic")
    print("=" * 40)
    
    check_env_file()
    check_config_loading()
    check_main_app_logic()
    
    print("\n" + "=" * 40)
    print("💡 Recommendations:")
    print("1. If you want 24/7 posting, ensure RESPECT_POSTING_HOURS_IN_INSTANT_MODE=false")
    print("2. Restart your main app after changing .env file")
    print("3. Check logs to see which setting is actually being used")

if __name__ == "__main__":
    main()
