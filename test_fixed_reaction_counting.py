#!/usr/bin/env python3
"""
Test the fixed reaction counting implementation
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_real_reaction_counting():
    """Test the fixed reaction counting logic"""
    print("🔧 Testing Fixed Reaction Counting")
    print("=" * 60)
    
    try:
        from src.telegram_client import TelegramClient
        
        # Create Bot API client
        bot_client = TelegramClient()
        
        print("🔄 Initializing Bot API client...")
        if await bot_client.initialize():
            print("✅ Bot API client initialized")
            
            # Get recent messages to test reaction counting
            print("\n📡 Getting recent messages to test reaction counting...")
            messages = await bot_client.get_recent_messages(limit=10, for_polling=True)
            
            if messages:
                print(f"✅ Retrieved {len(messages)} messages")
                
                # Analyze each message's reaction count
                tehran_tz = pytz.timezone('Asia/Tehran')
                now_tehran = datetime.now(tehran_tz)
                
                print(f"\n📊 Detailed Reaction Analysis:")
                print("=" * 80)
                
                total_reactions = 0
                messages_with_reactions = 0
                
                for i, msg in enumerate(messages):
                    msg_tehran = msg.date.astimezone(tehran_tz)
                    age = now_tehran - msg_tehran
                    age_hours = age.total_seconds() / 3600
                    
                    reaction_count = getattr(msg, 'reaction_count', 0)
                    
                    print(f"  {i+1:2d}. Message #{msg.message_id}")
                    print(f"      Time: {msg_tehran.strftime('%H:%M %d/%m')} ({age_hours:.1f}h ago)")
                    print(f"      Reactions: {reaction_count}")
                    print(f"      Text: {(msg.text or msg.caption or 'No text')[:60]}...")
                    
                    # Check for obvious fake counting
                    if reaction_count > 0:
                        messages_with_reactions += 1
                        total_reactions += reaction_count
                        
                        # Warn if reaction count seems suspiciously high for a message with no visible reactions
                        if reaction_count > 15:
                            print(f"      ⚠️  HIGH reaction count - verify this is real!")
                        else:
                            print(f"      ✅ Reasonable reaction count")
                    else:
                        print(f"      ✅ No reactions (honest count)")
                    print()
                
                # Summary
                print(f"📈 Summary:")
                print(f"   Total messages: {len(messages)}")
                print(f"   Messages with reactions: {messages_with_reactions}")
                print(f"   Average reactions: {total_reactions / len(messages):.1f}")
                
                # Check if the counting seems realistic
                if messages_with_reactions == 0:
                    print("✅ EXCELLENT: All messages show 0 reactions (honest counting)")
                    return True
                elif total_reactions / len(messages) < 5:
                    print("✅ GOOD: Low average reactions (realistic counting)")
                    return True
                else:
                    print("⚠️  HIGH average reactions - may still be fake counting")
                    return False
            else:
                print("❌ No messages retrieved")
                return False
        else:
            print("❌ Failed to initialize Bot API client")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        try:
            await bot_client.close()
        except:
            pass

async def test_message_selection_with_fixed_counting():
    """Test message selection with the fixed reaction counting"""
    print("\n🎯 Testing Message Selection with Fixed Counting")
    print("=" * 60)
    
    try:
        from src.message_processor import MessageProcessor
        
        # Create message processor
        processor = MessageProcessor()
        
        print("🔄 Testing message selection with fixed reaction counting...")
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected = processed_messages[0]
            msg_id = selected.telegram_message.message_id
            reactions = getattr(selected.telegram_message, 'reaction_count', 0)
            
            print(f"✅ Message Processor Selected:")
            print(f"📱 Message ID: #{msg_id}")
            print(f"🔥 Reactions: {reactions}")
            print(f"📝 Content: {selected.text_content[:100]}...")
            
            # Verify the reaction count is realistic
            if reactions == 0:
                print("✅ PERFECT: Selected message with 0 reactions (honest counting)")
                print("📝 This matches what you see in Telegram (no emoji reactions)")
                return True
            elif reactions <= 5:
                print("✅ GOOD: Selected message with low reaction count (realistic)")
                return True
            else:
                print(f"⚠️  Selected message claims {reactions} reactions - verify this is real")
                return False
        else:
            print("❌ No message selected")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Fixed Reaction Counting Test")
    print("=" * 70)
    
    print("🎯 Goal: Verify reaction counting is now accurate (not fake random numbers)")
    print("Expected: Message #22947 should show 0 reactions (as seen in your image)")
    print()
    
    # Test 1: Check reaction counting accuracy
    test1_result = await test_real_reaction_counting()
    
    # Test 2: Check message selection with fixed counting
    test2_result = await test_message_selection_with_fixed_counting()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS")
    print("=" * 70)
    
    print(f"Reaction Counting: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Message Selection: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 SUCCESS! Reaction counting is now accurate!")
        print("✅ No more fake random reaction numbers")
        print("✅ Honest reporting of actual reaction counts")
        print("✅ Message selection based on real engagement data")
        
        print("\n📝 What was fixed:")
        print("- Removed fake random number generation")
        print("- Implemented real reaction detection")
        print("- Added fallback to views/forwards data")
        print("- Returns 0 when no real engagement data available")
    else:
        print("\n⚠️  Some issues remain:")
        
        if not test1_result:
            print("🔧 Reaction counting still seems inaccurate")
        if not test2_result:
            print("🔧 Message selection logic needs adjustment")
    
    print("\n💡 Expected behavior now:")
    print("- Messages with no emoji reactions show 0 reactions")
    print("- Only messages with real engagement show positive counts")
    print("- Selection based on actual engagement, not fake numbers")
    print("- Honest reporting matches what you see in Telegram")

if __name__ == "__main__":
    asyncio.run(main())
