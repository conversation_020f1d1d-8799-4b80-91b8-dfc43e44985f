#!/usr/bin/env python3
"""
Setup script for audio refresh automation
Creates virtual environment and sets up the daily refresh scheduler
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, cwd=cwd, check=True, 
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def setup_virtual_environment():
    """Create and setup virtual environment for audio refresh"""
    print("=== Setting up Audio Refresh Virtual Environment ===")
    
    # Create audio directory if it doesn't exist
    audio_dir = Path("audio")
    audio_dir.mkdir(exist_ok=True)
    print(f"Audio directory: {audio_dir.absolute()}")
    
    # Create virtual environment in audio directory
    venv_path = audio_dir / "venv"
    
    if venv_path.exists():
        print(f"Virtual environment already exists at: {venv_path}")
        response = input("Do you want to recreate it? (y/N): ").strip().lower()
        if response == 'y':
            import shutil
            shutil.rmtree(venv_path)
        else:
            print("Using existing virtual environment")
    
    if not venv_path.exists():
        print("Creating virtual environment...")
        if not run_command(f"python -m venv {venv_path}"):
            print("Failed to create virtual environment")
            return False
    
    # Determine activation script path
    if platform.system() == "Windows":
        activate_script = venv_path / "Scripts" / "activate.bat"
        pip_path = venv_path / "Scripts" / "pip.exe"
        python_path = venv_path / "Scripts" / "python.exe"
    else:
        activate_script = venv_path / "bin" / "activate"
        pip_path = venv_path / "bin" / "pip"
        python_path = venv_path / "bin" / "python"
    
    print(f"Activation script: {activate_script}")
    print(f"Python path: {python_path}")
    
    # Install requirements
    print("Installing requirements...")
    if not run_command(f'"{pip_path}" install -r audio_refresh_requirements.txt'):
        print("Failed to install requirements")
        return False
    
    print("✅ Virtual environment setup completed!")
    return True, venv_path, python_path

def create_run_script(venv_path, python_path):
    """Create a script to run the audio refresh"""
    print("Creating run script...")
    
    if platform.system() == "Windows":
        script_content = f"""@echo off
REM Audio Refresh Runner Script
cd /d "{Path.cwd()}"
"{python_path}" audio_refresh_script.py
"""
        script_path = Path("run_audio_refresh.bat")
    else:
        script_content = f"""#!/bin/bash
# Audio Refresh Runner Script
cd "{Path.cwd()}"
"{python_path}" audio_refresh_script.py
"""
        script_path = Path("run_audio_refresh.sh")
    
    script_path.write_text(script_content)
    
    if not platform.system() == "Windows":
        # Make script executable on Unix systems
        os.chmod(script_path, 0o755)
    
    print(f"✅ Run script created: {script_path.absolute()}")
    return script_path

def create_test_script(venv_path, python_path):
    """Create a script to test the audio refresh immediately"""
    print("Creating test script...")
    
    if platform.system() == "Windows":
        script_content = f"""@echo off
REM Audio Refresh Test Script
cd /d "{Path.cwd()}"
"{python_path}" audio_refresh_script.py --run-now
pause
"""
        script_path = Path("test_audio_refresh.bat")
    else:
        script_content = f"""#!/bin/bash
# Audio Refresh Test Script
cd "{Path.cwd()}"
"{python_path}" audio_refresh_script.py --run-now
"""
        script_path = Path("test_audio_refresh.sh")
    
    script_path.write_text(script_content)
    
    if not platform.system() == "Windows":
        # Make script executable on Unix systems
        os.chmod(script_path, 0o755)
    
    print(f"✅ Test script created: {script_path.absolute()}")
    return script_path

def setup_windows_task_scheduler(script_path):
    """Setup Windows Task Scheduler for daily execution"""
    print("Setting up Windows Task Scheduler...")
    
    task_name = "AudioRefreshDaily"
    
    # Create task scheduler command
    schtasks_command = f'''schtasks /create /tn "{task_name}" /tr "{script_path.absolute()}" /sc daily /st 01:00 /f'''
    
    print(f"Creating scheduled task: {task_name}")
    print("This will run daily at 1:00 AM")
    
    if run_command(schtasks_command):
        print("✅ Windows Task Scheduler setup completed!")
        print(f"Task '{task_name}' created successfully")
        return True
    else:
        print("❌ Failed to create scheduled task")
        print("You may need to run this script as Administrator")
        return False

def setup_unix_cron(script_path):
    """Setup Unix cron job for daily execution"""
    print("Setting up cron job...")
    
    # Cron entry for 1:00 AM daily
    cron_entry = f"0 1 * * * {script_path.absolute()}"
    
    print("To setup the cron job, run the following commands:")
    print("1. Open crontab editor:")
    print("   crontab -e")
    print("2. Add this line:")
    print(f"   {cron_entry}")
    print("3. Save and exit")
    print()
    print("This will run the audio refresh daily at 1:00 AM")
    
    return True

def main():
    """Main setup function"""
    print("🎵 Audio Refresh Setup Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("audio_refresh_script.py").exists():
        print("❌ audio_refresh_script.py not found in current directory")
        print("Please run this script from the project root directory")
        return
    
    # Setup virtual environment
    result = setup_virtual_environment()
    if not result:
        print("❌ Failed to setup virtual environment")
        return
    
    success, venv_path, python_path = result
    
    # Create run scripts
    run_script = create_run_script(venv_path, python_path)
    test_script = create_test_script(venv_path, python_path)
    
    # Setup scheduler based on OS
    print("\n=== Setting up Scheduler ===")
    
    if platform.system() == "Windows":
        setup_windows_task_scheduler(run_script)
    else:
        setup_unix_cron(run_script)
    
    print("\n=== Setup Complete! ===")
    print("📁 Files created:")
    print(f"  - Virtual environment: {venv_path}")
    print(f"  - Run script: {run_script}")
    print(f"  - Test script: {test_script}")
    print()
    print("🧪 To test the audio refresh immediately:")
    if platform.system() == "Windows":
        print(f"  {test_script}")
    else:
        print(f"  ./{test_script}")
    print()
    print("⏰ The audio refresh will run automatically every day at 1:00 AM Tehran time")
    print("📝 Check 'audio_refresh.log' for execution logs")
    print()
    print("🔧 Optional: Set PIXABAY_API_KEY environment variable for better music variety")

if __name__ == "__main__":
    main()
