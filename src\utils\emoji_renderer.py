"""
Emoji rendering utility that converts emojis to images
"""
import os
import requests
from pathlib import Path
from PIL import Image, ImageDraw
import emoji
import re
from ..utils.logger import logger

class EmojiRenderer:
    """Handles emoji rendering by converting them to images"""
    
    def __init__(self, emoji_size=32):
        self.emoji_size = emoji_size
        self.emoji_cache_dir = Path("emoji_cache")
        self.emoji_cache_dir.mkdir(exist_ok=True)
        
        # Common emoji to Unicode mapping
        self.emoji_map = {
            '😊': '1f60a',
            '🎉': '1f389', 
            '❤️': '2764-fe0f',
            '⭐': '2b50',
            '🔥': '1f525',
            '👍': '1f44d',
            '🇮🇷': '1f1ee-1f1f7',
            '😍': '1f60d',
            '😂': '1f602',
            '🥰': '1f970',
            '💯': '1f4af',
            '🚀': '1f680',
            '💪': '1f4aa',
            '🙏': '1f64f',
            '✨': '2728',
            '🌟': '1f31f',
            '💖': '1f496',
            '🎯': '1f3af',
            '📱': '1f4f1',
            '💻': '1f4bb',
            '🎵': '1f3b5',
            '🎶': '1f3b6',
            '📸': '1f4f8',
            '🎬': '1f3ac',
            '🏆': '1f3c6',
            '🎊': '1f38a',
            '🌈': '1f308',
            '☀️': '2600-fe0f',
            '🌙': '1f319',
            '💫': '1f4ab',
            '🔮': '1f52e',
            '🎨': '1f3a8',
            '🖼️': '1f5bc-fe0f',
            '📝': '1f4dd',
            '✅': '2705',
            '❌': '274c',
            '⚡': '26a1',
            '🔴': '1f534',
            '🟢': '1f7e2',
            '🔵': '1f535',
            '🟡': '1f7e1',
            '🟠': '1f7e0',
            '🟣': '1f7e3'
        }
    
    def get_emoji_unicode(self, emoji_char):
        """Get Unicode code for emoji"""
        if emoji_char in self.emoji_map:
            return self.emoji_map[emoji_char]
        
        # Fallback: convert to hex
        try:
            code_points = [hex(ord(c))[2:] for c in emoji_char]
            return '-'.join(code_points)
        except:
            return None
    
    def download_emoji_image(self, emoji_char):
        """Download emoji image from online source"""
        try:
            unicode_code = self.get_emoji_unicode(emoji_char)
            if not unicode_code:
                return None
            
            # Try different emoji image sources
            sources = [
                f"https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/{unicode_code}.png",
                f"https://raw.githubusercontent.com/twitter/twemoji/master/assets/72x72/{unicode_code}.png"
            ]
            
            cache_path = self.emoji_cache_dir / f"{unicode_code}.png"
            
            # Check if already cached
            if cache_path.exists():
                return cache_path
            
            # Try to download
            for source_url in sources:
                try:
                    response = requests.get(source_url, timeout=10)
                    if response.status_code == 200:
                        with open(cache_path, 'wb') as f:
                            f.write(response.content)
                        logger.info(f"Downloaded emoji image: {emoji_char} -> {cache_path}")
                        return cache_path
                except:
                    continue
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to download emoji {emoji_char}: {e}")
            return None
    
    def create_emoji_placeholder(self, emoji_char):
        """Create a simple colored placeholder for emoji"""
        try:
            # Create a simple colored circle as placeholder
            img = Image.new('RGBA', (self.emoji_size, self.emoji_size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Different colors for different emojis
            color_map = {
                '😊': '#FFD700',  # Gold
                '🎉': '#FF6B6B',  # Red
                '❤️': '#FF1744',  # Red
                '⭐': '#FFD700',  # Gold
                '🔥': '#FF5722',  # Orange
                '👍': '#4CAF50',  # Green
                '🇮🇷': '#4CAF50',  # Green
            }
            
            color = color_map.get(emoji_char, '#2196F3')  # Default blue
            
            # Draw circle
            margin = 4
            draw.ellipse([margin, margin, self.emoji_size-margin, self.emoji_size-margin], 
                        fill=color, outline='white', width=2)
            
            return img
            
        except Exception as e:
            logger.warning(f"Failed to create placeholder for {emoji_char}: {e}")
            return None
    
    def get_emoji_image(self, emoji_char):
        """Get emoji image (download or create placeholder)"""
        try:
            # First try to download
            image_path = self.download_emoji_image(emoji_char)
            if image_path and image_path.exists():
                img = Image.open(image_path)
                # Resize to desired size
                img = img.resize((self.emoji_size, self.emoji_size), Image.Resampling.LANCZOS)
                return img
            
            # Fallback to placeholder
            return self.create_emoji_placeholder(emoji_char)
            
        except Exception as e:
            logger.warning(f"Failed to get emoji image for {emoji_char}: {e}")
            return None
    
    def render_text_with_emojis(self, draw, position, text, font, fill):
        """Render text with emojis replaced by images"""
        try:
            import emoji as emoji_lib
            
            x, y = position
            current_x = x
            
            # Split text into emoji and non-emoji parts
            parts = []
            current_part = ""
            
            for char in text:
                if emoji_lib.is_emoji(char):
                    if current_part:
                        parts.append(('text', current_part))
                        current_part = ""
                    parts.append(('emoji', char))
                else:
                    current_part += char
            
            if current_part:
                parts.append(('text', current_part))
            
            # Render each part
            for part_type, content in parts:
                if part_type == 'text':
                    # Regular text
                    try:
                        draw.text((current_x, y), content, font=font, fill=fill)
                        bbox = font.getbbox(content)
                        current_x += bbox[2] - bbox[0]
                    except:
                        current_x += len(content) * (font.size // 2)
                        
                elif part_type == 'emoji':
                    # Emoji image
                    emoji_img = self.get_emoji_image(content)
                    if emoji_img:
                        # Calculate position to align with text baseline
                        emoji_y = y + (font.size - self.emoji_size) // 2
                        
                        # Paste emoji image
                        if emoji_img.mode == 'RGBA':
                            draw._image.paste(emoji_img, (int(current_x), int(emoji_y)), emoji_img)
                        else:
                            draw._image.paste(emoji_img, (int(current_x), int(emoji_y)))
                        
                        current_x += self.emoji_size
                    else:
                        # Fallback: skip emoji
                        current_x += self.emoji_size // 2
            
            return True
            
        except Exception as e:
            logger.error(f"Error rendering text with emojis: {e}")
            # Fallback to regular text rendering
            try:
                clean_text = emoji_lib.demojize(text)
                draw.text(position, clean_text, font=font, fill=fill)
                return True
            except:
                return False
