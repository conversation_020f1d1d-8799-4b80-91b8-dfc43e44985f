# 🎬 LinkInsta - Automated Instagram Reel Generator

**LinkInsta** is a powerful automation tool that converts Telegram channel messages into professional Instagram reels with perfect Farsi text support, emoji rendering, and beautiful visual assets.

## ✨ Features

### 🎯 **Core Functionality**
- **📡 Telegram Integration**: Automatically fetches messages from @linkychannel
- **🎬 Video Generation**: Creates Instagram-ready reels in multiple formats
- **📱 Instagram Posting**: Automated posting to Instagram (configurable)
- **🤖 Telegram Bot**: Sends generated videos to personal chat

### 🎨 **Visual Excellence**
- **😊 Perfect Emoji Support**: Real emoji images (not broken rectangles)
- **📝 Farsi Text Mastery**: Correct line ordering and character rendering
- **📱 Responsive Design**: Text automatically fits within containers
- **🎨 Professional Assets**: Beautiful backgrounds and @linkychannell branding
- **🐦 Twitter-style Layout**: Modern, engaging visual design

### 🧠 **Smart Processing**
- **🔍 Content Filtering**: Automatically skips V2ray configs
- **📋 Multi-format Support**: Text-only, Text+Image, Video+Text
- **⚡ Performance**: Local emoji caching for fast rendering
- **🛡️ Error Handling**: Robust processing with comprehensive logging

## 🚀 Quick Start

### 1. **Installation**

```bash
git clone https://github.com/daadbina/linkyinsta.git
cd linkyinsta
pip install -r requirements.txt
```

### 2. **Configuration**

Copy `.env.example` to `.env` and configure:

```env
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=-1001069766340
TELEGRAM_PERSONAL_CHAT_ID=142183523

# Instagram Configuration
INSTAGRAM_USERNAME=linkychannell
INSTAGRAM_PASSWORD=your_password

# Feature Toggles
DEMO_MODE=false
ENABLE_INSTAGRAM_POSTING=false
ENABLE_TELEGRAM_VIDEO_SENDING=true
```

### 3. **Run**

```bash
# Generate videos from latest channel messages
python generate_from_channel.py

# Or run the full automation
python main.py
```

## 📋 Configuration Options

| Setting | Description | Default |
|---------|-------------|---------|
| `DEMO_MODE` | Skip Instagram login completely | `false` |
| `ENABLE_INSTAGRAM_POSTING` | Actually post to Instagram | `false` |
| `ENABLE_TELEGRAM_VIDEO_SENDING` | Send videos to personal chat | `true` |
| `TELEGRAM_PERSONAL_CHAT_ID` | Your personal chat ID for videos | `142183523` |

## 🎬 Video Types

### 📝 **Text-Only Videos**
- Twitter-style layout with professional backgrounds
- Multi-line Farsi text with perfect rendering
- Responsive font sizing
- Real emoji images

### 🖼️ **Text + Image Videos**
- Combines text overlay with downloaded images
- Maintains aspect ratio and quality
- Professional text containers

### 🎥 **Video + Text Videos**
- Overlays text on existing videos
- Subtitle-style text rendering
- Supports up to 4 lines of text

## 🔧 Technical Features

### 🌟 **Advanced Text Processing**
- **Farsi Line Ordering**: Correct reading sequence
- **Emoji Rendering**: Downloads Twitter's Twemoji images
- **Character Cleaning**: Handles problematic Unicode characters
- **Responsive Sizing**: Auto-adjusts font size to fit containers

### 🛡️ **Smart Filtering**
- **V2ray Detection**: Automatically skips config messages
- **Content Classification**: Identifies text, image, and video content
- **@linkychannel Removal**: Cleans channel tags from messages

### ⚡ **Performance**
- **Local Caching**: Emoji images cached for speed
- **Efficient Processing**: Batch operations and smart queuing
- **Error Recovery**: Comprehensive fallback systems

## 📁 Project Structure

```
linkyinsta/
├── src/
│   ├── video_generators/     # Video generation engines
│   ├── utils/               # Utilities (emoji renderer, logger)
│   ├── config.py           # Configuration management
│   ├── telegram_client.py  # Telegram integration
│   ├── instagram_client.py # Instagram integration
│   └── message_processor.py # Message processing logic
├── fonts/                  # Vazir font for Farsi text
├── backgrounds/           # Professional background images
├── audio/                # Background music files
├── output_videos/        # Generated videos
└── requirements.txt      # Python dependencies
```

## 🎯 Usage Examples

### Generate from Channel Messages
```python
python generate_from_channel.py
```

## 🔍 Troubleshooting

### Common Issues

**Emoji showing as rectangles?**
- The system now uses real emoji images from Twitter's Twemoji
- Check `emoji_cache/` directory for downloaded emojis

**Text outside container bounds?**
- Responsive sizing automatically adjusts font size
- Multi-line support handles long text properly

**V2ray configs being processed?**
- Enhanced detection automatically skips these messages
- Check logs for "Skipping V2ray config" messages

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **Twitter Twemoji**: For beautiful emoji images
- **Vazir Font**: For excellent Farsi text rendering
- **Telegram Bot API**: For seamless integration

## 📞 Support

For support and questions:
- 📧 Create an issue on GitHub
- 💬 Contact via Telegram: @linkychannell

---

**🎯 LinkInsta - Perfect Instagram Reels from Telegram Messages** 🚀
