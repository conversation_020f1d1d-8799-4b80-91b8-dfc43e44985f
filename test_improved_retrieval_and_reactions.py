#!/usr/bin/env python3
"""
Test improved message retrieval and reaction counting
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_improved_message_retrieval():
    """Test the improved message retrieval to get more messages"""
    print("🔧 Testing Improved Message Retrieval")
    print("=" * 60)
    
    try:
        from src.telegram_client import TelegramClient
        
        # Create Bot API client
        bot_client = TelegramClient()
        
        print("🔄 Initializing Bot API client...")
        if await bot_client.initialize():
            print("✅ Bot API client initialized")
            
            # Get recent messages with improved retrieval
            print("\n📡 Getting recent messages with improved retrieval...")
            messages = await bot_client.get_recent_messages(limit=15, for_polling=True)
            
            if messages:
                print(f"✅ Retrieved {len(messages)} messages (improved from 1!)")
                
                # Analyze each message
                tehran_tz = pytz.timezone('Asia/Tehran')
                now_tehran = datetime.now(tehran_tz)
                
                print(f"\n📊 Detailed Message Analysis:")
                print("=" * 80)
                
                messages_with_reactions = 0
                total_reactions = 0
                
                for i, msg in enumerate(messages):
                    msg_tehran = msg.date.astimezone(tehran_tz)
                    age = now_tehran - msg_tehran
                    age_hours = age.total_seconds() / 3600
                    
                    reaction_count = getattr(msg, 'reaction_count', 0)
                    
                    print(f"  {i+1:2d}. Message #{msg.message_id}")
                    print(f"      Time: {msg_tehran.strftime('%H:%M %d/%m')} ({age_hours:.1f}h ago)")
                    print(f"      Reactions: {reaction_count}")
                    print(f"      Text: {(msg.text or msg.caption or 'No text')[:60]}...")
                    
                    if reaction_count > 0:
                        messages_with_reactions += 1
                        total_reactions += reaction_count
                        print(f"      🔥 HAS REACTIONS!")
                    else:
                        print(f"      📝 No reactions detected")
                    print()
                
                # Summary
                print(f"📈 Retrieval Summary:")
                print(f"   Total messages retrieved: {len(messages)}")
                print(f"   Messages with detected reactions: {messages_with_reactions}")
                print(f"   Total reactions detected: {total_reactions}")
                
                if len(messages) > 1:
                    print("✅ IMPROVED: Now getting multiple messages instead of just 1!")
                else:
                    print("⚠️  Still only getting 1 message - alternative method needs more work")
                
                return messages
            else:
                print("❌ No messages retrieved")
                return []
        else:
            print("❌ Failed to initialize Bot API client")
            return []
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return []
    
    finally:
        try:
            await bot_client.close()
        except:
            pass

async def test_reaction_detection_accuracy():
    """Test if the app can detect the message with 2 reactions you mentioned"""
    print("\n🎯 Testing Reaction Detection Accuracy")
    print("=" * 60)
    
    try:
        from src.message_processor import MessageProcessor
        
        # Create message processor
        processor = MessageProcessor()
        
        print("🔄 Testing message selection with improved detection...")
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected = processed_messages[0]
            msg_id = selected.telegram_message.message_id
            reactions = getattr(selected.telegram_message, 'reaction_count', 0)
            
            print(f"✅ Message Processor Selected:")
            print(f"📱 Message ID: #{msg_id}")
            print(f"🔥 Detected Reactions: {reactions}")
            print(f"📝 Content: {selected.text_content[:100]}...")
            
            # Check if this matches your observation
            if msg_id == 22949:
                print(f"\n🎯 This is message #22949 that you mentioned!")
                if reactions == 0:
                    print("❌ PROBLEM: You said it has 2 reactions but app detects 0")
                    print("🔧 This confirms Bot API cannot access reaction data")
                    print("💡 Bot API limitation: Reactions not accessible for channels")
                elif reactions == 2:
                    print("✅ PERFECT: Correctly detected 2 reactions!")
                else:
                    print(f"⚠️  Detected {reactions} reactions, you said it has 2")
            
            return {
                'id': msg_id,
                'detected_reactions': reactions,
                'content': selected.text_content[:60]
            }
        else:
            print("❌ No message selected")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def main():
    """Main test function"""
    print("🚀 Improved Retrieval and Reaction Detection Test")
    print("=" * 70)
    
    print("🎯 Goals:")
    print("1. Get more than 1 message (fix alternative method)")
    print("2. Detect the 2 reactions on message #22949")
    print("3. Understand Bot API limitations")
    print()
    
    # Test 1: Improved message retrieval
    messages = await test_improved_message_retrieval()
    
    # Test 2: Reaction detection accuracy
    selected_info = await test_reaction_detection_accuracy()
    
    # Analysis and recommendations
    print("\n" + "=" * 70)
    print("📊 ANALYSIS AND RECOMMENDATIONS")
    print("=" * 70)
    
    if len(messages) > 1:
        print("✅ Message retrieval improved - getting multiple messages")
    else:
        print("⚠️  Still limited to 1 message - Bot API constraint")
    
    if selected_info:
        if selected_info['detected_reactions'] == 0:
            print("❌ Reaction detection issue confirmed")
            print("🔧 RECOMMENDATION: Bot API cannot access channel reactions")
            print("💡 SOLUTIONS:")
            print("   1. Use User API for reaction access (more complex)")
            print("   2. Accept Bot API limitation and use other criteria")
            print("   3. Use views/forwards as engagement proxy")
            print("   4. Manual selection or time-based selection")
        else:
            print("✅ Reaction detection working!")
    
    print(f"\n📝 CURRENT STATUS:")
    print(f"- Message retrieval: {'✅ Multiple messages' if len(messages) > 1 else '⚠️  Limited to 1'}")
    print(f"- Reaction detection: {'❌ Bot API limitation' if selected_info and selected_info['detected_reactions'] == 0 else '✅ Working'}")
    print(f"- App functionality: ✅ Working (creates videos, posts to platforms)")
    
    print(f"\n💡 NEXT STEPS:")
    if len(messages) <= 1:
        print("🔧 Improve alternative method to get more messages")
    if selected_info and selected_info['detected_reactions'] == 0:
        print("🔧 Consider alternative selection criteria (time-based, content-based)")
        print("🔧 Or implement User API for full reaction access")

if __name__ == "__main__":
    asyncio.run(main())
