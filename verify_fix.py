#!/usr/bin/env python3
"""
Simple verification that the fix is working
"""
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def main():
    print("🔧 Verifying Posting Hours Fix")
    print("=" * 40)
    
    # Check current configuration
    from src.config import Config
    
    print(f"📱 Instagram posting: {Config.ENABLE_INSTAGRAM_POSTING}")
    print(f"📺 YouTube posting: {Config.ENABLE_YOUTUBE_POSTING}")
    print(f"⚡ Instant posting mode: {Config.INSTANT_POSTING_MODE}")
    print(f"🕐 Respect posting hours: {Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE}")
    print(f"🕘 Posting window: {Config.POSTING_START_HOUR}:00 - {Config.POSTING_END_HOUR}:00")
    
    print("\n✅ Fix Applied:")
    print("- When outside posting hours + restriction enabled:")
    print("  → Content is skipped gracefully (returns True)")
    print("  → Sends notification about time restriction")
    print("  → No error message logged")
    
    print("\n- When outside posting hours + restriction disabled:")
    print("  → Content is posted immediately")
    print("  → Works 24/7 as before")
    
    print("\n🎯 The error you saw is now fixed!")
    print("Instead of: '❌ Failed to post message X instantly'")
    print("You'll see: '✅ Successfully processed message X instantly'")
    print("And a notification about time restrictions.")

if __name__ == "__main__":
    main()
