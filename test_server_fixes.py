#!/usr/bin/env python3
"""
Test the server fixes: admin chat sending and performance optimization
"""
import asyncio
import sys
import time
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import Config
from src.main_app import LinkInstaApp
from src.video_generators import TextVideoGenerator, VideoTextGenerator

async def test_admin_chat_sending():
    """Test sending videos to admin chat"""
    print("📱 Testing Admin Chat Video Sending")
    print("=" * 50)
    
    Config.create_directories()
    
    # Create a test video
    generator = TextVideoGenerator()
    test_text = "🧪 Test video for admin chat verification"
    test_video_path = Config.OUTPUT_DIR / "admin_chat_test.mp4"
    
    print(f"📹 Generating test video...")
    success = generator.generate_video(test_text, test_video_path)
    
    if not success or not test_video_path.exists():
        print(f"❌ Failed to generate test video")
        return False
    
    print(f"✅ Test video generated: {test_video_path.name}")
    
    # Test sending to admin chat
    try:
        from src.telegram_client import TelegramClient
        
        caption = f"🧪 Test Video from Server\n\n"
        caption += f"📝 This is a test to verify admin chat functionality\n"
        caption += f"🕒 Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        caption += f"📁 File: {test_video_path.name}\n\n"
        caption += f"✅ If you receive this, admin chat is working!"
        
        telegram_client = TelegramClient()
        success = await telegram_client.send_video(
            video_path=test_video_path,
            caption=caption,
            chat_id=Config.TELEGRAM_PERSONAL_CHAT_ID
        )
        
        if success:
            print(f"✅ Test video sent to admin chat: {Config.TELEGRAM_PERSONAL_CHAT_ID}")
            print(f"📱 Check your Telegram for the test video!")
        else:
            print(f"❌ Failed to send test video to admin chat")
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing admin chat: {e}")
        return False

def test_video_performance():
    """Test video generation performance with optimizations"""
    print(f"\n⚡ Testing Video Generation Performance")
    print("=" * 50)
    
    Config.create_directories()
    
    test_cases = [
        {
            "name": "Text-Only Video",
            "generator": TextVideoGenerator(),
            "method": "generate_video",
            "args": ["Performance test text for measuring encoding speed", Config.OUTPUT_DIR / "perf_test_text.mp4"]
        }
    ]
    
    # Test if we have a sample video for video+text testing
    sample_video = None
    for video_file in Config.OUTPUT_DIR.glob("*.mp4"):
        if video_file.stat().st_size > 100000:  # At least 100KB
            sample_video = video_file
            break
    
    if sample_video:
        test_cases.append({
            "name": "Video+Text Processing",
            "generator": VideoTextGenerator(),
            "method": "generate_video", 
            "args": ["Performance test overlay text", sample_video, Config.OUTPUT_DIR / "perf_test_video_text.mp4"]
        })
    
    results = []
    
    for test_case in test_cases:
        print(f"\n🧪 Testing {test_case['name']}:")
        
        try:
            generator = test_case['generator']
            method = getattr(generator, test_case['method'])
            
            # Measure time
            start_time = time.time()
            success = method(*test_case['args'])
            end_time = time.time()
            
            duration = end_time - start_time
            
            if success:
                print(f"   ✅ Generated successfully")
                print(f"   ⏱️  Time taken: {duration:.2f} seconds")
                
                # Check output file
                output_path = test_case['args'][-1]
                if output_path.exists():
                    size_mb = output_path.stat().st_size / (1024 * 1024)
                    print(f"   📊 File size: {size_mb:.1f} MB")
                
                results.append({
                    'name': test_case['name'],
                    'success': True,
                    'duration': duration,
                    'size_mb': size_mb if 'size_mb' in locals() else 0
                })
            else:
                print(f"   ❌ Generation failed")
                results.append({
                    'name': test_case['name'],
                    'success': False,
                    'duration': duration,
                    'size_mb': 0
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({
                'name': test_case['name'],
                'success': False,
                'duration': 0,
                'size_mb': 0
            })
    
    return results

def test_configuration():
    """Test configuration settings"""
    print(f"\n⚙️  Testing Configuration")
    print("=" * 50)
    
    config_checks = [
        ("Telegram Bot Token", Config.TELEGRAM_BOT_TOKEN, "Should be set"),
        ("Telegram Chat ID", Config.TELEGRAM_CHAT_ID, "Should be -1001069766340"),
        ("Personal Chat ID", Config.TELEGRAM_PERSONAL_CHAT_ID, "Should be 142183523"),
        ("Channel Username", Config.TELEGRAM_CHANNEL_USERNAME, "Should be @linkychannel"),
        ("Output Directory", Config.OUTPUT_DIR, "Should exist"),
        ("Temp Directory", Config.TEMP_DIR, "Should exist"),
    ]
    
    all_good = True
    
    for name, value, expected in config_checks:
        print(f"📋 {name}:")
        
        if name == "Telegram Bot Token":
            if value and len(str(value)) > 10:
                print(f"   ✅ Set (length: {len(str(value))})")
            else:
                print(f"   ❌ Not set or invalid")
                all_good = False
        
        elif name in ["Output Directory", "Temp Directory"]:
            if value and Path(value).exists():
                print(f"   ✅ Exists: {value}")
            else:
                print(f"   ❌ Missing: {value}")
                all_good = False
        
        else:
            print(f"   📝 Value: {value}")
            print(f"   📋 Expected: {expected}")
            if str(value) == expected.split()[-1]:
                print(f"   ✅ Correct")
            else:
                print(f"   ⚠️  Check if this is intentional")
    
    return all_good

async def main():
    """Main test function"""
    print("🔧 Server Fixes Test")
    print("=" * 60)
    
    # Test configuration
    config_ok = test_configuration()
    
    # Test video performance
    perf_results = test_video_performance()
    
    # Test admin chat sending
    admin_chat_ok = await test_admin_chat_sending()
    
    print(f"\n" + "=" * 60)
    print("📊 Server Fixes Test Results:")
    
    # Configuration
    print(f"\n⚙️  Configuration: {'✅ OK' if config_ok else '❌ Issues'}")
    
    # Performance
    print(f"\n⚡ Performance Results:")
    for result in perf_results:
        status = "✅" if result['success'] else "❌"
        print(f"   {status} {result['name']}: {result['duration']:.1f}s, {result['size_mb']:.1f}MB")
    
    # Admin chat
    print(f"\n📱 Admin Chat: {'✅ Working' if admin_chat_ok else '❌ Failed'}")
    
    # Overall assessment
    all_working = config_ok and admin_chat_ok and all(r['success'] for r in perf_results)
    
    print(f"\n🎯 Overall Status: {'✅ ALL FIXES WORKING' if all_working else '❌ Some issues remain'}")
    
    if all_working:
        print(f"\n🎉 Server is ready for production!")
        print(f"   • Videos will be sent to admin chat: {Config.TELEGRAM_PERSONAL_CHAT_ID}")
        print(f"   • Performance optimized for faster processing")
        print(f"   • All generators working properly")
    else:
        print(f"\n⚠️  Please check the issues above before deploying")
    
    print(f"\n📁 Generated test files:")
    output_dir = Config.OUTPUT_DIR
    test_files = list(output_dir.glob("*test*"))
    for file in test_files:
        if file.suffix == '.mp4':
            size_mb = file.stat().st_size / (1024 * 1024)
            print(f"   🎥 {file.name} ({size_mb:.1f} MB)")

if __name__ == "__main__":
    asyncio.run(main())
