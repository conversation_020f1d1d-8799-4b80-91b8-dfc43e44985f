#!/usr/bin/env python3
"""
Test script to verify Instagram posting notifications
"""
import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.scheduler import PostScheduler
from src.utils.logger import logger
import logging

async def test_notifications():
    """Test notification functionality"""
    print("=" * 60)
    print("Testing Instagram Posting Notifications")
    print("=" * 60)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    scheduler = PostScheduler()
    
    # Test 1: Send schedule summary
    print("\n1. Testing schedule summary notification...")
    try:
        scheduler.send_schedule_summary()
        print("   ✅ Schedule summary sent successfully")
    except Exception as e:
        print(f"   ❌ Schedule summary failed: {e}")
    
    # Test 2: Test direct notification
    print("\n2. Testing direct notification...")
    try:
        test_message = (
            "🧪 <b>Test Notification</b>\n\n"
            "This is a test notification from the Instagram posting system.\n\n"
            "✅ All notification systems are working correctly!"
        )
        success = scheduler._send_notification_sync(test_message)
        if success:
            print("   ✅ Direct notification sent successfully")
        else:
            print("   ❌ Direct notification failed")
    except Exception as e:
        print(f"   ❌ Direct notification error: {e}")
    
    # Test 3: Show current schedule info
    print("\n3. Current schedule information...")
    try:
        from datetime import datetime
        import pytz
        
        now_tehran = datetime.now(scheduler.tehran_tz)
        print(f"   Current Tehran time: {now_tehran.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Total scheduled posts: {len(scheduler.scheduled_posts)}")
        
        # Count posts by status
        status_counts = {}
        for post in scheduler.scheduled_posts:
            status = post.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print("   Posts by status:")
        for status, count in status_counts.items():
            print(f"     {status}: {count}")
            
    except Exception as e:
        print(f"   ❌ Error getting schedule info: {e}")
    
    print("\n" + "=" * 60)
    print("Notification tests completed")
    print("=" * 60)

def test_sync_notifications():
    """Test synchronous notification functionality"""
    print("🔔 Testing Instagram Posting Notifications")
    print("==========================================")
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    scheduler = PostScheduler()
    
    # Test schedule summary
    print("\n📅 Sending schedule summary...")
    try:
        scheduler.send_schedule_summary()
        print("✅ Schedule summary sent!")
    except Exception as e:
        print(f"❌ Schedule summary failed: {e}")
    
    # Test success notification simulation
    print("\n✅ Sending success notification simulation...")
    try:
        success_message = (
            "✅ <b>Instagram Post Successful! (TEST)</b>\n\n"
            "📱 <b>Media ID:</b> TEST_123456789\n"
            "🕐 <b>Posted at:</b> 2025-07-24 10:30 (Tehran)\n"
            "📝 <b>Caption:</b> This is a test post to verify notifications are working correctly.\n\n"
            "🔗 @linkychannell"
        )
        scheduler._send_notification_sync(success_message)
        print("✅ Success notification sent!")
    except Exception as e:
        print(f"❌ Success notification failed: {e}")
    
    # Test error notification simulation
    print("\n❌ Sending error notification simulation...")
    try:
        error_message = (
            "❌ <b>Instagram Posting Failed! (TEST)</b>\n\n"
            "🕐 <b>Failed at:</b> 2025-07-24 10:30 (Tehran)\n"
            "📝 <b>Caption:</b> This is a test error notification.\n"
            "🔄 <b>Attempt:</b> 1/3\n\n"
            "Will retry if attempts remaining."
        )
        scheduler._send_notification_sync(error_message)
        print("✅ Error notification sent!")
    except Exception as e:
        print(f"❌ Error notification failed: {e}")

if __name__ == "__main__":
    try:
        # Run synchronous tests
        test_sync_notifications()
        
        print("\n🎉 All notification tests completed!")
        print("\nCheck your Telegram admin chat to see the notifications.")
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
