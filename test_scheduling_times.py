#!/usr/bin/env python3
"""
Test script to verify scheduling works correctly at different times of day
"""
import sys
from pathlib import Path
from datetime import datetime, time
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_scheduling_at_different_times():
    """Test scheduling logic at different times of day"""
    print("🕐 Testing Scheduling at Different Times")
    print("=" * 60)
    
    try:
        from src.scheduler import PostScheduler
        
        # Create scheduler
        scheduler = PostScheduler()
        tehran_tz = scheduler.tehran_tz
        
        # Test scenarios at different times
        test_times = [
            ("01:30", "Early morning (outside posting window)"),
            ("08:30", "Just before posting window"),
            ("09:30", "Early in posting window"),
            ("11:50", "Mid-morning (your test time)"),
            ("13:58", "Early afternoon (current time)"),
            ("15:30", "Mid-afternoon"),
            ("18:00", "Evening"),
            ("22:30", "Late evening"),
            ("23:30", "Just before posting window ends"),
        ]
        
        for time_str, description in test_times:
            print(f"\n📅 Testing at {time_str} - {description}")
            
            # Parse test time
            hour, minute = map(int, time_str.split(':'))
            
            # Create a test datetime for today at this time
            today = datetime.now(tehran_tz).date()
            test_datetime = tehran_tz.localize(
                datetime.combine(today, time(hour=hour, minute=minute))
            )
            
            print(f"   Test time: {test_datetime.strftime('%Y-%m-%d %H:%M %Z')}")
            
            # Temporarily modify the scheduler's time calculation
            original_now = datetime.now
            
            def mock_now(tz=None):
                if tz == tehran_tz:
                    return test_datetime
                elif tz is None:
                    return test_datetime.astimezone(pytz.UTC).replace(tzinfo=None)
                else:
                    return test_datetime.astimezone(tz)
            
            # Monkey patch datetime.now
            import datetime as dt_module
            dt_module.datetime.now = mock_now
            
            try:
                # Test the scheduling logic
                next_slot = scheduler._get_next_available_slot()
                next_slot_tehran = pytz.UTC.localize(next_slot).astimezone(tehran_tz)
                
                print(f"   Next slot: {next_slot_tehran.strftime('%Y-%m-%d %H:%M %Z')}")
                
                # Check if it's scheduled for today or tomorrow
                if next_slot_tehran.date() == today:
                    print(f"   ✅ Correctly scheduled for TODAY")
                else:
                    print(f"   ❌ Incorrectly scheduled for TOMORROW")
                    
                # Check if the time makes sense
                if hour < 9:  # Before posting window
                    expected_hour = 9  # Should schedule for 9 AM today
                    if next_slot_tehran.hour >= expected_hour and next_slot_tehran.date() == today:
                        print(f"   ✅ Correctly scheduled for later today")
                    else:
                        print(f"   ❌ Should schedule for later today, not tomorrow")
                        
                elif 9 <= hour < 24:  # In posting window
                    if next_slot_tehran.hour >= hour and next_slot_tehran.date() == today:
                        print(f"   ✅ Correctly scheduled for current/future hour today")
                    else:
                        print(f"   ❌ Should schedule for current/future hour today")
                        
            except Exception as e:
                print(f"   ❌ Error testing time {time_str}: {e}")
            
            finally:
                # Restore original datetime.now
                dt_module.datetime.now = original_now
        
        return True
        
    except Exception as e:
        print(f"❌ Scheduling time test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_current_real_time():
    """Test with current real time"""
    print("\n🕐 Testing with Current Real Time")
    print("=" * 60)
    
    try:
        from src.scheduler import PostScheduler
        
        scheduler = PostScheduler()
        
        # Get current time
        now_tehran = datetime.now(scheduler.tehran_tz)
        print(f"Current Tehran time: {now_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"Current hour: {now_tehran.hour}")
        print(f"Posting window: {scheduler.posting_start_hour}-{scheduler.posting_end_hour}")
        print(f"In posting window: {scheduler.posting_start_hour <= now_tehran.hour < scheduler.posting_end_hour}")
        
        # Test scheduling
        next_slot = scheduler._get_next_available_slot()
        next_slot_tehran = pytz.UTC.localize(next_slot).astimezone(scheduler.tehran_tz)
        
        print(f"Next available slot: {next_slot_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        # Check if it's today or tomorrow
        today = now_tehran.date()
        if next_slot_tehran.date() == today:
            print("✅ Scheduled for TODAY")
        else:
            print("❌ Scheduled for TOMORROW")
            
        # If we're in posting window, it should be scheduled for today
        if scheduler.posting_start_hour <= now_tehran.hour < scheduler.posting_end_hour:
            if next_slot_tehran.date() == today:
                print("✅ Correctly scheduled for today (we're in posting window)")
            else:
                print("❌ ERROR: Should be scheduled for today since we're in posting window!")
        
        return True
        
    except Exception as e:
        print(f"❌ Real time test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all scheduling time tests"""
    print("🚀 Testing Scheduling Time Logic")
    print("=" * 80)
    
    tests = [
        test_current_real_time,
        test_scheduling_at_different_times,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All scheduling time fixes are working correctly!")
        return True
    else:
        print("⚠️  Some scheduling time issues still exist")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ Scheduling time logic verified successfully!")
        else:
            print("\n❌ Scheduling time issues detected!")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback
        traceback.print_exc()
