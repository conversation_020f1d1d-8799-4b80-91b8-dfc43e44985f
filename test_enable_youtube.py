#!/usr/bin/env python3
"""
Test script to enable YouTube posting and verify it works
"""
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def update_env_file():
    """Update .env file to enable YouTube posting"""
    env_file = Path('.env')
    
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Read current content
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update YouTube posting setting
    if 'ENABLE_YOUTUBE_POSTING=false' in content:
        content = content.replace('ENABLE_YOUTUBE_POSTING=false', 'ENABLE_YOUTUBE_POSTING=true')
        print("✅ Updated ENABLE_YOUTUBE_POSTING to true")
    elif 'ENABLE_YOUTUBE_POSTING=true' in content:
        print("✅ ENABLE_YOUTUBE_POSTING already set to true")
    else:
        print("❌ ENABLE_YOUTUBE_POSTING not found in .env file")
        return False
    
    # Write back
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def test_config_reload():
    """Test that config reloads with YouTube enabled"""
    # Force reload of config
    if 'src.config' in sys.modules:
        del sys.modules['src.config']
    
    from src.config import Config
    
    print(f"📺 YouTube Posting Enabled: {Config.ENABLE_YOUTUBE_POSTING}")
    print(f"📱 Instagram Posting Enabled: {Config.ENABLE_INSTAGRAM_POSTING}")
    print(f"🧪 Test Mode: {Config.TEST_MODE}")
    
    return Config.ENABLE_YOUTUBE_POSTING

def main():
    """Main function"""
    print("🔧 Enabling YouTube Posting...")
    print("=" * 40)
    
    # Update .env file
    if not update_env_file():
        print("❌ Failed to update .env file")
        return False
    
    # Test config reload
    if not test_config_reload():
        print("❌ YouTube posting not enabled in config")
        return False
    
    print("\n✅ YouTube posting is now enabled!")
    print("\n📋 Next steps:")
    print("1. Run your main app: python main.py")
    print("2. Videos will now be posted to both Instagram and YouTube")
    print("3. Check logs for posting status")
    print("\n⚠️  Note: Make sure you have content in your Telegram channel to process")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
