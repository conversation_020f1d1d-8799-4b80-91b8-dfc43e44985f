"""
Video + Text generator for creating Instagram reels from existing videos with text overlay
"""
import os
import random
from pathlib import Path
from typing import Optional, List, Tuple

from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, ImageClip, concatenate_videoclips
import arabic_reshaper
from bidi.algorithm import get_display

from src.config import Config
from src.utils.logger import logger
from src.video_generators.text_video_generator import TextVideoGenerator

class VideoTextGenerator(TextVideoGenerator):
    """Generates Instagram reel videos from existing videos with text overlay"""
    
    def __init__(self):
        super().__init__()
        self.target_aspect_ratio = self.height / self.width  # 16:9 -> 9:16 for Instagram reels
    
    def _resize_video_to_reel_format(self, video_clip: VideoFileClip) -> VideoFileClip:
        """Position video beautifully within Instagram reel format while preserving original dimensions"""
        original_width, original_height = video_clip.size
        original_aspect_ratio = original_height / original_width

        # Calculate the best fit while preserving aspect ratio
        # Scale to fit within the target dimensions without cropping
        scale_width = self.width / original_width
        scale_height = self.height / original_height
        scale_factor = min(scale_width, scale_height)

        # Calculate new dimensions (preserving aspect ratio)
        new_width = int(original_width * scale_factor)
        new_height = int(original_height * scale_factor)

        # Resize the video to the calculated dimensions
        resized_clip = video_clip.resize((new_width, new_height))

        # Create a beautiful background for positioning
        # Calculate position to center the video
        x_offset = (self.width - new_width) // 2
        y_offset = (self.height - new_height) // 2

        # Create gradient background
        background = self._create_gradient_background()

        # Position the video on the gradient background
        final_clip = CompositeVideoClip([
            ImageClip(background, duration=resized_clip.duration),
            resized_clip.set_position((x_offset, y_offset))
        ], size=(self.width, self.height))

        return final_clip

    def _create_gradient_background(self) -> np.ndarray:
        """Create a beautiful gradient background for video positioning"""
        import numpy as np

        # Create gradient background
        background = np.zeros((self.height, self.width, 3), dtype=np.uint8)

        # Create radial gradient from center
        center_x, center_y = self.width // 2, self.height // 2
        max_distance = np.sqrt(center_x**2 + center_y**2)

        for y in range(self.height):
            for x in range(self.width):
                # Calculate distance from center
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                factor = distance / max_distance

                # Create gradient colors (dark blue to black)
                r = int(15 * (1 - factor))
                g = int(25 * (1 - factor))
                b = int(35 * (1 - factor))

                background[y, x] = [r, g, b]

        return background
    
    def _create_text_overlay_image(self, text: str, video_width: int, video_height: int) -> Image.Image:
        """Create an enhanced text overlay image with better positioning and styling"""
        try:
            # Prepare Farsi text
            display_text = self._prepare_farsi_text(text)

            # Create transparent overlay image
            overlay = Image.new('RGBA', (video_width, video_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)

            # Enhanced text area settings - larger font and better positioning
            text_area_width = video_width - 120  # Better margins
            text_area_height = 300  # More height for larger text

            # Calculate optimal font size with increased minimum size
            optimal_font_size, required_height, lines = self._calculate_optimal_font_and_layout(
                display_text, text_area_width, text_area_height
            )

            # Increase font size for better readability
            optimal_font_size = max(optimal_font_size, int(video_height * 0.04))  # Minimum 4% of video height

            # Create responsive font
            try:
                responsive_font = ImageFont.truetype(str(self.font_path), optimal_font_size)

                # If we didn't get lines from the calculation, wrap them now
                if not lines:
                    lines = self._wrap_text_with_font(display_text, text_area_width, responsive_font)
            except:
                # Fallback font and simple wrapping
                responsive_font = ImageFont.load_default()
                words = display_text.split()
                lines = []
                current_line = []
                max_chars_per_line = 30

                for word in words:
                    if len(' '.join(current_line + [word])) <= max_chars_per_line:
                        current_line.append(word)
                    else:
                        if current_line:
                            lines.append(' '.join(current_line))
                            current_line = [word]
                        else:
                            lines.append(word)

                if current_line:
                    lines.append(' '.join(current_line))

            # Limit to 4 lines for overlay
            if len(lines) > 4:
                lines = lines[:4]
                if len(lines[3]) > 40:
                    lines[3] = lines[3][:37] + "..."

            # Calculate text positioning with better placement
            line_height = int(optimal_font_size * 1.5)  # Better line spacing
            total_text_height = len(lines) * line_height

            # Position text higher up for better visibility - in the upper third
            start_y = int(video_height * 0.25)  # 25% from top instead of bottom

            # Create horizontal background rectangle for text readability
            bg_padding = 30
            bg_width = video_width - 80  # Leave margins
            bg_height = total_text_height + (2 * bg_padding)
            bg_x = (video_width - bg_width) // 2
            bg_y = int(start_y - bg_padding)

            # Create gradient background for text
            text_bg = Image.new('RGBA', (bg_width, bg_height), (0, 0, 0, 0))
            text_bg_draw = ImageDraw.Draw(text_bg)

            # Create horizontal gradient background
            for y in range(bg_height):
                factor = abs(y - bg_height // 2) / (bg_height // 2)  # Center-out gradient
                alpha = int(180 * (1 - factor * 0.3))  # Stronger in center
                text_bg_draw.line([(0, y), (bg_width, y)], fill=(0, 0, 0, alpha))

            # Create rounded rectangle mask
            bg_mask = Image.new('L', (bg_width, bg_height), 0)
            bg_mask_draw = ImageDraw.Draw(bg_mask)
            bg_mask_draw.rounded_rectangle([0, 0, bg_width, bg_height], radius=20, fill=255)
            text_bg.putalpha(bg_mask)

            # Paste background onto overlay
            overlay.paste(text_bg, (bg_x, bg_y), text_bg)

            # Draw each line with enhanced stroke effect and better positioning
            for i, line in enumerate(lines):
                if not line.strip():
                    continue

                # Calculate line position (centered horizontally) with emoji-aware width
                try:
                    line_width = self._calculate_text_width_with_emojis(line, responsive_font)
                except:
                    line_width = int(len(line) * (optimal_font_size * 0.6))

                x = int((video_width - line_width) // 2)
                y = int(start_y + (i * line_height))

                # Draw enhanced stroke (black outline) for better readability
                stroke_width = 4  # Increased stroke width
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx*dx + dy*dy <= stroke_width*stroke_width:
                            try:
                                self._draw_text_with_emoji(draw, (x + dx, y + dy), line, responsive_font, (0, 0, 0, 200))
                            except:
                                draw.text((x + dx, y + dy), line, font=responsive_font, fill=(0, 0, 0, 200))

                # Draw main text (white with slight shadow)
                try:
                    self._draw_text_with_emoji(draw, (x, y), line, responsive_font, (255, 255, 255, 255))
                except:
                    draw.text((x, y), line, font=responsive_font, fill=(255, 255, 255, 255))

            return overlay

        except Exception as e:
            logger.error(f"Error creating text overlay image: {e}")
            # Return empty transparent image
            return Image.new('RGBA', (video_width, video_height), (0, 0, 0, 0))
    
    def _add_branding_to_overlay(self, overlay_image: Image.Image) -> Image.Image:
        """Add enhanced channel branding to overlay image"""
        try:
            draw = ImageDraw.Draw(overlay_image)
            brand_text = f"@{Config.INSTAGRAM_HANDLE}"

            # Create branding font with better size
            brand_font_size = int(self.font_size * 0.6)  # Slightly larger
            try:
                brand_font = ImageFont.truetype(str(self.font_path), brand_font_size)
            except:
                brand_font = ImageFont.load_default()

            # Calculate position (top right with better margins)
            try:
                bbox = brand_font.getbbox(brand_text)
                text_width = int(bbox[2] - bbox[0])
                text_height = int(bbox[3] - bbox[1])
            except:
                text_width = int(len(brand_text) * (brand_font_size * 0.6))
                text_height = int(brand_font_size)

            x = int(overlay_image.width - text_width - 30)
            y = int(30)

            # Create background for brand text
            bg_padding = 15
            bg_width = int(text_width + (2 * bg_padding))
            bg_height = int(text_height + (2 * bg_padding))
            bg_x = int(x - bg_padding)
            bg_y = int(y - bg_padding)

            # Create gradient background for brand
            brand_bg = Image.new('RGBA', (bg_width, bg_height), (0, 0, 0, 0))
            brand_bg_draw = ImageDraw.Draw(brand_bg)

            for y_bg in range(bg_height):
                factor = y_bg / bg_height
                r = int(self.accent_color[0] * (1 - factor * 0.2))
                g = int(self.accent_color[1] * (1 - factor * 0.2))
                b = int(self.accent_color[2] * (1 - factor * 0.2))
                brand_bg_draw.line([(0, y_bg), (bg_width, y_bg)], fill=(r, g, b, 180))

            # Create rounded rectangle mask
            brand_mask = Image.new('L', (bg_width, bg_height), 0)
            brand_mask_draw = ImageDraw.Draw(brand_mask)
            brand_mask_draw.rounded_rectangle([0, 0, bg_width, bg_height], radius=12, fill=255)
            brand_bg.putalpha(brand_mask)

            # Paste brand background
            overlay_image.paste(brand_bg, (bg_x, bg_y), brand_bg)

            # Draw enhanced stroke (black outline)
            stroke_width = 2
            for dx in range(-stroke_width, stroke_width + 1):
                for dy in range(-stroke_width, stroke_width + 1):
                    if dx*dx + dy*dy <= stroke_width*stroke_width:
                        draw.text((x + dx, y + dy), brand_text, font=brand_font, fill=(0, 0, 0, 150))

            # Draw main text (white for better contrast)
            draw.text((x, y), brand_text, font=brand_font, fill=(255, 255, 255, 255))

            return overlay_image

        except Exception as e:
            logger.error(f"Error adding branding overlay: {e}")
            return overlay_image
    
    def _trim_video_to_duration(self, video_clip: VideoFileClip, target_duration: float) -> VideoFileClip:
        """Trim video to target duration"""
        if video_clip.duration <= target_duration:
            return video_clip
        
        # Take from the middle of the video for best content
        start_time = (video_clip.duration - target_duration) / 2
        end_time = start_time + target_duration
        
        return video_clip.subclip(start_time, end_time)
    
    def generate_video(self, text: str, video_path: Path, output_path: Path) -> bool:
        """
        Generate an Instagram reel from existing video with text overlay

        Args:
            text: The text content to overlay
            video_path: Path to the source video file
            output_path: Path where the processed video should be saved

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Generating video+text reel: {output_path}")

            if not video_path.exists():
                logger.error(f"Source video not found: {video_path}")
                return False

            # Load source video
            source_video = VideoFileClip(str(video_path))
            logger.info(f"Loaded source video: {source_video.duration:.2f}s, {source_video.size}")

            # Keep original duration - don't trim unless it's extremely long
            original_duration = source_video.duration
            if original_duration > 60:  # Only trim if longer than 60 seconds
                source_video = self._trim_video_to_duration(source_video, 60)
                logger.info(f"Trimmed very long video from {original_duration:.2f}s to 60s")
            else:
                logger.info(f"Keeping original duration: {original_duration:.2f}s")

            # Resize to Instagram reel format
            formatted_video = self._resize_video_to_reel_format(source_video)
            logger.info(f"Resized video to reel format: {formatted_video.size}")

            # For text + video messages, only add branding (no text overlay)
            # Create transparent overlay for branding only
            overlay_image = Image.new('RGBA', (formatted_video.w, formatted_video.h), (0, 0, 0, 0))
            overlay_image = self._add_branding_to_overlay(overlay_image)

            import numpy as np
            overlay_array = np.array(overlay_image)
            overlay_clip = ImageClip(overlay_array, duration=formatted_video.duration, ismask=False)

            final_video = CompositeVideoClip([formatted_video, overlay_clip])
            logger.info("Added branding overlay only (text overlay removed as requested)")

            # IMPORTANT: Preserve original audio - don't add background music
            if source_video.audio:
                final_video = final_video.set_audio(source_video.audio)
                logger.info("Preserved original video audio (no background music added)")
            else:
                logger.info("Source video has no audio")

            # Write final video with optimized settings for faster processing
            final_video.write_videofile(
                str(output_path),
                fps=self.fps,
                codec='libx264',
                audio_codec='aac' if source_video.audio else None,
                temp_audiofile=str(Config.TEMP_DIR / 'temp_audio.m4a') if source_video.audio else None,
                remove_temp=True,
                verbose=False,
                logger=None,
                # Optimization settings for faster encoding
                preset='ultrafast',  # Fastest encoding preset
                ffmpeg_params=['-crf', '23', '-threads', '4']  # Good quality, multi-threading
            )

            # Clean up video clips and force garbage collection
            source_video.close()
            formatted_video.close()
            final_video.close()

            # Force garbage collection to release file handles
            import gc
            gc.collect()

            logger.info(f"Successfully generated video+text reel: {output_path}")
            logger.info(f"Final video duration: {original_duration:.2f}s (preserved from original)")
            return True

        except Exception as e:
            logger.error(f"Error generating video+text reel: {e}")
            return False
    
    def extract_video_thumbnail(self, video_path: Path, output_path: Path, time_offset: float = None) -> bool:
        """
        Extract a thumbnail from the video
        
        Args:
            video_path: Path to the source video
            output_path: Path where thumbnail should be saved
            time_offset: Time offset to extract thumbnail (default: middle of video)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video_clip = VideoFileClip(str(video_path))
            
            if time_offset is None:
                time_offset = video_clip.duration / 2
            
            # Extract frame
            frame = video_clip.get_frame(time_offset)
            
            # Convert to PIL Image and save
            thumbnail = Image.fromarray(frame)
            thumbnail.save(output_path)
            
            video_clip.close()

            # Force garbage collection to release file handles
            import gc
            gc.collect()

            logger.info(f"Extracted thumbnail: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error extracting thumbnail: {e}")
            return False

    def _calculate_optimal_font_and_layout(self, text: str, max_width: int, initial_max_height: int) -> tuple:
        """Calculate optimal font size and required height for text layout"""
        # Use a good readable font size for video overlay
        preferred_font_size = min(self.font_size, 32)  # Good readable size for video overlay
        min_font_size = 20  # Minimum readable size for video

        # Try preferred font size first
        for test_font_size in [preferred_font_size, preferred_font_size - 3, preferred_font_size - 6, min_font_size]:
            try:
                test_font = ImageFont.truetype(str(self.font_path), test_font_size)

                # Test wrap with this font size
                lines = self._wrap_text_with_font(text, max_width, test_font)

                # Check if individual lines fit width using emoji-aware calculation
                fits_width = True
                for line in lines:
                    try:
                        line_width = self._calculate_text_width_with_emojis(line, test_font)
                        if line_width > max_width:
                            fits_width = False
                            break
                    except:
                        fits_width = False
                        break

                if fits_width and len(lines) <= 6:  # Allow up to 6 lines for video overlay
                    # Calculate required height
                    line_height = int(test_font_size * 1.4)
                    required_height = len(lines) * line_height + 30  # Extra padding

                    return test_font_size, required_height, lines

            except Exception:
                continue

        # Fallback
        return min_font_size, initial_max_height, []
