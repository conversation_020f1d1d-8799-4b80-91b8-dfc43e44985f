#!/usr/bin/env python3
"""
Test script to authenticate and upload a test video to Top Media YouTube channel
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Config
from video_generators.text_video_generator import TextVideoGenerator
from youtube_client import YouTubeClient
from utils.logger import logger


def test_topmedia_authentication():
    """Test Top Media YouTube authentication"""
    print("🔐 Testing Top Media YouTube Authentication...\n")
    
    try:
        # Initialize Top Media YouTube client
        topmedia_client = YouTubeClient("topmedia")
        
        print(f"📋 Configuration:")
        print(f"   Channel Type: topmedia")
        print(f"   Channel Name: {topmedia_client.channel_name}")
        print(f"   Client Secrets File: {topmedia_client.client_secrets_file}")
        print(f"   Credentials File: {topmedia_client.credentials_file}")
        print(f"   Channel ID: {topmedia_client.channel_id}")
        
        # Check if client secrets file exists
        if not topmedia_client.client_secrets_file.exists():
            print(f"❌ Client secrets file not found: {topmedia_client.client_secrets_file}")
            return False
        
        print(f"✅ Client secrets file found")
        
        # Attempt authentication
        print(f"\n🔑 Attempting authentication...")
        print(f"   This will open a browser window for OAuth authentication")
        print(f"   Please log in with the Top Media channel owner's Google account")
        
        auth_success = topmedia_client.authenticate()
        
        if auth_success:
            print(f"✅ Authentication successful!")
            
            # Get channel info
            channel_info = topmedia_client.get_channel_info()
            if channel_info:
                print(f"\n📺 Channel Information:")
                print(f"   Channel ID: {channel_info['id']}")
                print(f"   Channel Title: {channel_info['title']}")
                print(f"   Subscriber Count: {channel_info['subscriber_count']}")
                print(f"   Video Count: {channel_info['video_count']}")
                return True
            else:
                print(f"⚠️ Authentication successful but couldn't get channel info")
                return True
        else:
            print(f"❌ Authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during authentication: {e}")
        return False


def generate_test_video():
    """Generate a test video for Top Media"""
    print(f"\n🎬 Generating Test Video for Top Media...\n")
    
    try:
        # Create test content
        test_text = """🔥 تست کانال تاپ مدیا 🔥

این یک ویدیو تست برای کانال Top Media است.

✅ برندینگ جدید
✅ طراحی سیاه و سفید  
✅ هندل @topmedi

#TopMedia #Test #YouTube"""
        
        # Generate video with Top Media branding
        topmedia_generator = TextVideoGenerator("topmedia")
        test_output = Config.OUTPUT_DIR / "topmedia_test_upload.mp4"
        
        print(f"📝 Test content: {test_text[:50]}...")
        print(f"📁 Output file: {test_output.name}")
        
        success = topmedia_generator.generate_video(test_text, test_output)
        
        if success:
            print(f"✅ Test video generated successfully!")
            print(f"📹 Video path: {test_output}")
            return test_output
        else:
            print(f"❌ Failed to generate test video")
            return None
            
    except Exception as e:
        print(f"❌ Error generating test video: {e}")
        return None


def upload_test_video(video_path):
    """Upload test video to Top Media YouTube channel"""
    print(f"\n📤 Uploading Test Video to Top Media YouTube...\n")
    
    try:
        # Initialize Top Media YouTube client
        topmedia_client = YouTubeClient("topmedia")
        
        # Prepare video metadata
        title = "🔥 تست کانال تاپ مدیا - Top Media Test Video"
        description = """این یک ویدیو تست برای کانال Top Media است.

🎯 ویژگی‌های جدید:
✅ برندینگ اختصاصی @topmedi
✅ طراحی سیاه و سفید
✅ سیستم خودکار تولید محتوا

📱 این محتوا بر اساس توییت‌های شما ساخته شده است

#TopMedia #Test #YouTube #Shorts"""
        
        tags = ["topmedia", "test", "persian", "farsi", "shorts", "news"]
        
        print(f"📋 Upload Details:")
        print(f"   Title: {title}")
        print(f"   Description: {description[:100]}...")
        print(f"   Tags: {', '.join(tags)}")
        print(f"   Privacy: public")
        
        # Upload video
        print(f"\n🚀 Starting upload...")
        video_id = topmedia_client.upload_video(
            video_path=video_path,
            title=title,
            description=description,
            tags=tags,
            privacy_status="public"
        )
        
        if video_id:
            print(f"\n🎉 SUCCESS! Video uploaded to Top Media YouTube!")
            print(f"📺 Video ID: {video_id}")
            print(f"🔗 Video URL: https://www.youtube.com/watch?v={video_id}")
            print(f"📱 Channel URL: https://www.youtube.com/@topmedii")
            return True
        else:
            print(f"❌ Upload failed")
            return False
            
    except Exception as e:
        print(f"❌ Error uploading video: {e}")
        return False


def main():
    """Run complete test"""
    print("🚀 TOP MEDIA YOUTUBE CHANNEL TEST\n")
    print("=" * 60)
    
    # Create directories
    Config.create_directories()
    
    # Step 1: Test authentication
    print("STEP 1: Authentication")
    auth_success = test_topmedia_authentication()
    
    if not auth_success:
        print("\n❌ Authentication failed. Please check:")
        print("   1. Client secrets file is correct")
        print("   2. You're logging in with the Top Media channel owner's account")
        print("   3. YouTube Data API v3 is enabled in Google Cloud Console")
        return False
    
    # Step 2: Generate test video
    print("\n" + "=" * 60)
    print("STEP 2: Video Generation")
    video_path = generate_test_video()
    
    if not video_path:
        print("\n❌ Video generation failed")
        return False
    
    # Step 3: Upload test video
    print("\n" + "=" * 60)
    print("STEP 3: Video Upload")
    upload_success = upload_test_video(video_path)
    
    # Final result
    print("\n" + "=" * 60)
    if upload_success:
        print("🎉 COMPLETE SUCCESS!")
        print("\n✅ Top Media YouTube integration is working perfectly!")
        print("\n📋 What was tested:")
        print("   ✅ OAuth authentication with Top Media credentials")
        print("   ✅ Video generation with @topmedi branding")
        print("   ✅ Black/white theme applied correctly")
        print("   ✅ Video upload to Top Media YouTube channel")
        print("\n🔗 Check your video at: https://www.youtube.com/@topmedii")
        return True
    else:
        print("❌ Test failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
