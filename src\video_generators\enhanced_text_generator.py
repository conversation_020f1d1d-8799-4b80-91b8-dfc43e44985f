"""
Enhanced text video generator with graphical assets
"""
import random
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from src.video_generators.text_video_generator import TextVideoGenerator
from src.config import Config
from src.utils.logger import logger

class EnhancedTextVideoGenerator(TextVideoGenerator):
    """Enhanced text video generator with professional assets"""
    
    
    def _get_random_background(self) -> Optional[Path]:
        """Get a random background image"""
        try:
            bg_dir = Config.BASE_DIR / "assets" / "backgrounds"
            if bg_dir.exists():
                bg_files = list(bg_dir.glob("*.png")) + list(bg_dir.glob("*.jpg"))
                if bg_files:
                    return random.choice(bg_files)
        except Exception as e:
            logger.warning(f"Could not get background: {e}")
        return None
    
    def _get_brand_logo(self, size: str = "medium") -> Optional[Path]:
        """Get the brand logo"""
        try:
            logo_path = Config.BASE_DIR / "assets" / "logos" / f"linky_logo_{size}.png"
            if logo_path.exists():
                return logo_path
        except Exception as e:
            logger.warning(f"Could not get logo: {e}")
        return None
    
    
    def _create_enhanced_frame(self, text: str) -> Image.Image:
        """Create enhanced frame with background and assets"""
        # Get random background
        bg_path = self._get_random_background()
        if bg_path:
            try:
                background = Image.open(bg_path).resize((self.width, self.height))
            except:
                background = Image.new('RGB', (self.width, self.height), self.background_color)
        else:
            background = Image.new('RGB', (self.width, self.height), self.background_color)
        
        # Create text overlay
        text_frame = self._create_twitter_like_frame(text)
        
        # Blend background with text frame
        if background.mode != 'RGBA':
            background = background.convert('RGBA')
        if text_frame.mode != 'RGBA':
            text_frame = text_frame.convert('RGBA')
        
        # Composite the images
        result = Image.alpha_composite(background, text_frame)
        
        # Add logo
        logo_path = self._get_brand_logo("small")
        if logo_path:
            try:
                logo = Image.open(logo_path).resize((80, 80))
                if logo.mode == 'RGBA':
                    result.paste(logo, (self.width - 100, 20), logo)
                else:
                    result.paste(logo, (self.width - 100, 20))
            except Exception as e:
                logger.warning(f"Could not add logo: {e}")
        
        return result.convert('RGB')
