#!/usr/bin/env python3
"""
Test the new content-based selection system
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_content_based_selection():
    """Test the new content-based selection system"""
    print("🎯 Testing Content-Based Selection System")
    print("=" * 60)
    
    try:
        from src.message_processor import MessageProcessor
        
        # Create message processor
        processor = MessageProcessor()
        
        print("🔄 Testing improved message selection...")
        processed_messages = await processor._poll_for_instant_posting()
        
        if processed_messages:
            selected = processed_messages[0]
            msg_id = selected.telegram_message.message_id
            reactions = getattr(selected.telegram_message, 'reaction_count', 0)
            
            print(f"✅ NEW SELECTION SYSTEM RESULT:")
            print(f"📱 Message ID: #{msg_id}")
            print(f"🔥 Reactions: {reactions} (Bot API limitation)")
            print(f"📝 Content: {selected.text_content}")
            print()
            
            # Calculate and show content score
            content_score = processor._calculate_content_score(selected.telegram_message)
            print(f"📊 Content Quality Score: {content_score:.2f}")
            
            # Analyze why this message was selected
            print(f"\n🔍 Selection Analysis:")
            text_length = len(selected.text_content)
            print(f"   Text length: {text_length} characters")
            
            if text_length > 200:
                print("   ✅ Long content bonus (****)")
            elif text_length > 100:
                print("   ✅ Medium content bonus (****)")
            elif text_length > 50:
                print("   ✅ Short content bonus (+0.5)")
            
            if hasattr(selected.telegram_message, 'photo') and selected.telegram_message.photo:
                print("   ✅ Photo bonus (****)")
            if hasattr(selected.telegram_message, 'video') and selected.telegram_message.video:
                print("   ✅ Video bonus (****)")
            
            # Check recency
            tehran_tz = pytz.timezone('Asia/Tehran')
            now_tehran = datetime.now(tehran_tz)
            msg_tehran = selected.telegram_message.date.astimezone(tehran_tz)
            age = now_tehran - msg_tehran
            age_hours = age.total_seconds() / 3600
            
            print(f"   Age: {age_hours:.1f} hours")
            if age_hours < 2:
                print("   ✅ Very recent bonus (****)")
            elif age_hours < 6:
                print("   ✅ Recent bonus (+0.5)")
            
            return {
                'id': msg_id,
                'score': content_score,
                'content': selected.text_content[:100],
                'age_hours': age_hours
            }
        else:
            print("❌ No message selected")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_app_with_new_selection():
    """Test the app with the new selection system"""
    print("\n🚀 Testing App with New Selection System")
    print("=" * 60)
    
    try:
        from src.main_app import LinkInstaApp
        
        print("🔄 Creating app instance...")
        app = LinkInstaApp()
        print("✅ App created successfully")
        
        print("\n📱 App is ready with:")
        print("✅ Content-based message selection (Bot API compatible)")
        print("✅ Instagram posting with Farsi attribution")
        print("✅ YouTube Shorts integration")
        print("✅ No more dependency on reaction data")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Content-Based Selection Test")
    print("=" * 70)
    
    print("🎯 NEW APPROACH:")
    print("Since Bot API cannot access reactions, we now use:")
    print("✅ Content length (longer = more engaging)")
    print("✅ Media presence (photos/videos = more engaging)")
    print("✅ Recency (newer = more relevant)")
    print("✅ Content quality indicators")
    print()
    
    # Test the new selection system
    selection_result = await test_content_based_selection()
    
    # Test app readiness
    app_ready = await test_app_with_new_selection()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 NEW SYSTEM RESULTS")
    print("=" * 70)
    
    if selection_result:
        print(f"✅ Selected Message: #{selection_result['id']}")
        print(f"📊 Content Score: {selection_result['score']:.2f}")
        print(f"⏰ Age: {selection_result['age_hours']:.1f} hours")
        print(f"📝 Content: {selection_result['content']}...")
        
        if selection_result['score'] > 2.0:
            print("🏆 HIGH QUALITY content selected!")
        elif selection_result['score'] > 1.0:
            print("✅ GOOD QUALITY content selected")
        else:
            print("📝 BASIC content selected")
    
    if app_ready:
        print("\n🎉 YOUR APP IS READY!")
        print("✅ No more Bot API reaction limitations")
        print("✅ Smart content-based selection")
        print("✅ Reliable message processing")
        print("✅ Dual-platform posting (Instagram + YouTube)")
        
        print(f"\n💡 HOW IT WORKS NOW:")
        print(f"1. Gets recent messages (within Bot API limits)")
        print(f"2. Scores content based on quality indicators")
        print(f"3. Selects highest-scoring content")
        print(f"4. Creates engaging videos")
        print(f"5. Posts to Instagram + YouTube with Farsi attribution")
        
        print(f"\n🎯 RECOMMENDATION:")
        print(f"✅ This approach is more reliable than reaction-based selection")
        print(f"✅ Works consistently with Bot API limitations")
        print(f"✅ Focuses on content quality rather than engagement metrics")
    else:
        print("\n⚠️  App needs attention")

if __name__ == "__main__":
    asyncio.run(main())
