#!/usr/bin/env python3
"""
Simple test for Instagram posting notifications
"""
import sys
import os
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

# Set up environment
os.environ.setdefault('TELEGRAM_BOT_TOKEN', '**********************************************')
os.environ.setdefault('TELEGRAM_PERSONAL_CHAT_ID', '142183523')

async def test_telegram_notification():
    """Test sending a notification to admin Telegram"""
    try:
        from src.telegram_client import TelegramClient
        
        print("🔔 Testing Telegram notification...")
        
        telegram_client = TelegramClient()
        await telegram_client.initialize()
        
        # Test message
        test_message = (
            "🧪 <b>Instagram Notification System Test</b>\n\n"
            "✅ This is a test notification from your Instagram posting system.\n\n"
            "📅 <b>Features added:</b>\n"
            "• Schedule summaries\n"
            "• Success notifications\n"
            "• Error notifications\n"
            "• Failure alerts\n\n"
            "🔗 @linkychannell"
        )
        
        # Send to admin chat
        success = await telegram_client.send_message(
            142183523,  # Admin chat ID
            test_message,
            parse_mode="HTML"
        )
        
        if success:
            print("✅ Test notification sent successfully!")
        else:
            print("❌ Failed to send test notification")
            
        await telegram_client.close()
        return success
        
    except Exception as e:
        print(f"❌ Error testing notification: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_schedule_summary():
    """Test schedule summary functionality"""
    try:
        from src.telegram_client import TelegramClient
        from datetime import datetime
        
        print("📅 Testing schedule summary...")
        
        telegram_client = TelegramClient()
        await telegram_client.initialize()
        
        # Create a sample schedule summary
        now = datetime.now()
        summary_message = (
            f"📅 <b>Instagram Schedule Summary</b>\n\n"
            f"🕐 <b>Current time:</b> {now.strftime('%Y-%m-%d %H:%M')} (Tehran)\n\n"
            f"📊 <b>Upcoming posts (Next 24h):</b>\n"
            f"1. <b>12:30</b> - Sample post about technology...\n"
            f"2. <b>15:45</b> - Another sample post...\n"
            f"3. <b>19:20</b> - Evening post content...\n\n"
            f"📈 <b>Total scheduled:</b> 3 posts\n\n"
            f"🔗 @linkychannell"
        )
        
        # Send to admin chat
        success = await telegram_client.send_message(
            142183523,  # Admin chat ID
            summary_message,
            parse_mode="HTML"
        )
        
        if success:
            print("✅ Schedule summary sent successfully!")
        else:
            print("❌ Failed to send schedule summary")
            
        await telegram_client.close()
        return success
        
    except Exception as e:
        print(f"❌ Error testing schedule summary: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Instagram Notification System Test")
    print("=====================================")
    
    # Test basic notification
    await test_telegram_notification()
    
    # Wait a bit
    await asyncio.sleep(2)
    
    # Test schedule summary
    await test_schedule_summary()
    
    print("\n🎉 All tests completed!")
    print("Check your Telegram admin chat for the notifications.")

if __name__ == "__main__":
    asyncio.run(main())
