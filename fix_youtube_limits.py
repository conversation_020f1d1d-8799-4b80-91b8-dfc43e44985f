#!/usr/bin/env python3
"""
Quick fix for YouTube upload limit exceeded error

This script temporarily disables YouTube posting to prevent repeated upload failures
when the daily upload limit is exceeded.
"""

import os
import sys
from pathlib import Path

def fix_youtube_limits():
    """Temporarily disable YouTube posting due to upload limits"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Read the current .env file
    with open(env_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Update the ENABLE_YOUTUBE_POSTING line
    updated_lines = []
    youtube_line_found = False
    
    for line in lines:
        if line.strip().startswith('ENABLE_YOUTUBE_POSTING='):
            updated_lines.append('ENABLE_YOUTUBE_POSTING=false  # Temporarily disabled due to upload limits\n')
            youtube_line_found = True
            print(f"📝 Updated: {line.strip()} -> ENABLE_YOUTUBE_POSTING=false")
        else:
            updated_lines.append(line)
    
    if not youtube_line_found:
        print("⚠️  ENABLE_YOUTUBE_POSTING not found in .env file")
        return False
    
    # Write the updated .env file
    with open(env_file, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)
    
    print("✅ YouTube posting has been temporarily disabled")
    print("💡 This prevents repeated upload failures due to daily limits")
    print("🔄 You can re-enable it tomorrow by setting ENABLE_YOUTUBE_POSTING=true")
    print("📋 Or use: python manage_youtube_limits.py enable")
    
    return True

if __name__ == "__main__":
    print("🚫 YouTube Upload Limit Fix")
    print("=" * 40)
    fix_youtube_limits()
