"""
Tests for message processor functionality
"""
import pytest
import asyncio
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch, AsyncMock

from src.message_processor import MessageProcessor, ProcessedMessage
from src.telegram_client import TelegramMessage
from src.config import Config

class TestMessageProcessor:
    """Test cases for MessageProcessor"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_config(self, temp_dir):
        """Mock configuration for tests"""
        with patch.object(Config, 'BASE_DIR', temp_dir):
            with patch.object(Config, 'TEMP_DIR', temp_dir / 'temp'):
                Config.TEMP_DIR.mkdir(exist_ok=True)
                yield Config
    
    @pytest.fixture
    def message_processor(self, mock_config):
        """Create MessageProcessor instance for testing"""
        return MessageProcessor()
    
    def test_processed_message_creation(self):
        """Test ProcessedMessage creation and properties"""
        # Create mock telegram message
        message_data = {
            'message_id': 12345,
            'text': 'Test message',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        telegram_msg = TelegramMessage(message_data)
        
        # Create processed message
        processed_msg = ProcessedMessage(telegram_msg)
        
        assert processed_msg.content_type == 'text_only'
        assert processed_msg.text_content == 'Test message'
        assert not processed_msg.should_skip
        assert not processed_msg.video_generated
        assert not processed_msg.instagram_posted
    
    def test_v2ray_config_detection(self):
        """Test V2ray config message detection"""
        # Create V2ray config message
        message_data = {
            'message_id': 12346,
            'text': 'کانفیگ رایگان شما آماده است',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        telegram_msg = TelegramMessage(message_data)
        processed_msg = ProcessedMessage(telegram_msg)
        
        assert processed_msg.should_skip
        assert processed_msg.content_type == 'v2ray_config'
    
    def test_content_type_detection(self):
        """Test different content type detection"""
        # Text only
        text_data = {
            'message_id': 1,
            'text': 'Just text',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        text_msg = TelegramMessage(text_data)
        assert ProcessedMessage(text_msg).content_type == 'text_only'
        
        # Text with image
        image_data = {
            'message_id': 2,
            'text': 'Text with image',
            'date': None,
            'media_type': 'photo',
            'media_files': [{'type': 'photo'}],
            'caption': ''
        }
        image_msg = TelegramMessage(image_data)
        assert ProcessedMessage(image_msg).content_type == 'text_image'
        
        # Video with text
        video_data = {
            'message_id': 3,
            'text': 'Video with text',
            'date': None,
            'media_type': 'video',
            'media_files': [{'type': 'video'}],
            'caption': ''
        }
        video_msg = TelegramMessage(video_data)
        assert ProcessedMessage(video_msg).content_type == 'video_text'
    
    @pytest.mark.asyncio
    async def test_poll_and_process_messages(self, message_processor):
        """Test polling and processing messages"""
        # Mock telegram client
        mock_telegram_client = Mock()
        mock_telegram_client.initialize = AsyncMock(return_value=True)
        mock_telegram_client.get_recent_messages = AsyncMock(return_value=[])
        mock_telegram_client.close = AsyncMock()
        
        message_processor.telegram_client = mock_telegram_client
        
        # Test with no messages
        result = await message_processor.poll_and_process_messages()
        assert result == []
        
        # Test with mock messages
        mock_message_data = {
            'message_id': 123,
            'text': 'Test message',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        mock_message = TelegramMessage(mock_message_data)
        mock_telegram_client.get_recent_messages.return_value = [mock_message]
        
        result = await message_processor.poll_and_process_messages()
        assert len(result) == 1
        assert result[0].telegram_message.message_id == 123
    
    def test_get_pending_messages(self, message_processor):
        """Test getting pending messages"""
        # Add some mock processed messages
        message_data1 = {
            'message_id': 1,
            'text': 'Pending text',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        message_data2 = {
            'message_id': 2,
            'text': 'کانفیگ رایگان شما',  # V2ray config - should be skipped
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        
        msg1 = ProcessedMessage(TelegramMessage(message_data1))
        msg2 = ProcessedMessage(TelegramMessage(message_data2))
        
        message_processor.processing_queue = [msg1, msg2]
        
        # Get pending messages
        pending = message_processor.get_pending_messages()
        assert len(pending) == 1  # Only msg1, msg2 should be skipped
        assert pending[0].telegram_message.message_id == 1
        
        # Test filtering by content type
        pending_text = message_processor.get_pending_messages('text_only')
        assert len(pending_text) == 1
        
        pending_image = message_processor.get_pending_messages('text_image')
        assert len(pending_image) == 0
    
    def test_mark_video_generated(self, message_processor, temp_dir):
        """Test marking video as generated"""
        message_data = {
            'message_id': 123,
            'text': 'Test message',
            'date': None,
            'media_type': None,
            'media_files': [],
            'caption': ''
        }
        
        processed_msg = ProcessedMessage(TelegramMessage(message_data))
        message_processor.processing_queue = [processed_msg]
        
        video_path = temp_dir / 'test_video.mp4'
        video_path.write_text("fake video content")
        
        # Mark as generated
        message_processor.mark_video_generated(processed_msg, video_path)
        
        assert processed_msg.video_generated
        assert processed_msg.output_video_path == video_path
    
    def test_get_statistics(self, message_processor):
        """Test getting processing statistics"""
        # Add mock messages with different states
        messages_data = [
            {'message_id': 1, 'text': 'Normal message', 'date': None, 'media_type': None, 'media_files': [], 'caption': ''},
            {'message_id': 2, 'text': 'کانفیگ رایگان شما', 'date': None, 'media_type': None, 'media_files': [], 'caption': ''},  # V2ray
            {'message_id': 3, 'text': 'Generated message', 'date': None, 'media_type': None, 'media_files': [], 'caption': ''},
            {'message_id': 4, 'text': 'Posted message', 'date': None, 'media_type': None, 'media_files': [], 'caption': ''}
        ]
        
        processed_messages = []
        for data in messages_data:
            msg = ProcessedMessage(TelegramMessage(data))
            processed_messages.append(msg)
        
        # Set different states
        processed_messages[2].video_generated = True  # Generated
        processed_messages[3].video_generated = True
        processed_messages[3].instagram_posted = True  # Posted
        
        message_processor.processing_queue = processed_messages
        
        stats = message_processor.get_statistics()
        
        assert stats['total_messages'] == 4
        assert stats['skipped_messages'] == 1  # V2ray config
        assert stats['videos_generated'] == 2
        assert stats['instagram_posted'] == 1
        assert stats['pending_processing'] == 1  # Total - skipped - generated

if __name__ == '__main__':
    pytest.main([__file__])
